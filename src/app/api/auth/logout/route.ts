import { routePaths } from '@/config';
import { COOKIE_NAMES } from '@/constants/auth';
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://beta.studify.vn';
  let redirectUrl = `${baseUrl}${routePaths.login}`;

  const { searchParams } = new URL(request.url);
  const pathname = searchParams.get('pathname');
  const query = searchParams.get('query');

  if (pathname) {
    const encodedRedirect = encodeURIComponent(`${pathname}${query ? `?${query}` : ''}`);
    redirectUrl = `${baseUrl}${routePaths.login}?redirect=${encodedRedirect}`;
  }

  const options = { maxAge: 0, domain: '.studify.vn', httpOnly: true, path: '/' };

  const response = NextResponse.redirect(redirectUrl, { status: 302 });

  response.cookies.set(COOKIE_NAMES.ACCESS_TOKEN, '', options);
  response.cookies.set(COOKIE_NAMES.REFRESH_TOKEN, '', options);
  response.cookies.set(COOKIE_NAMES.USER_INFO, '', options);

  return response;
}
