'use server';

import { getCookiesAsString } from '@/actions/cookieAction';
import { redirectPath } from '@/actions/redirectPath';
import { HTTP_STATUS_CODE, NEXT_API_ENDPOINTS } from '@/constants/api';
import https from 'https';
import { headers } from 'next/headers';

interface ApiResponse<T> {
  success: boolean;
  data: T | null;
  status: number;
  message: string;
}

interface FetchConfig {
  options?: RequestInit;
  maxRetries?: number;
  retryDelay?: (attempt: number) => number;
}

function createHeaders(cookie: string, customHeaders?: HeadersInit): HeadersInit {
  return {
    Cookie: cookie,
    ...customHeaders,
  };
}

async function parseResponse<T>(res: Response): Promise<ApiResponse<T>> {
  const data = await res.json();
  return { success: true, data, status: res.status, message: res.statusText };
}

export async function serverFetcher<T>(url: string, config: FetchConfig = {}): Promise<ApiResponse<T>> {
  const { options } = config;

  const cookie = await getCookiesAsString();

  const agent = process.env.NODE_ENV === 'development' ? new https.Agent({ rejectUnauthorized: false }) : undefined;

  try {
    const res = await fetch(url, {
      ...options,
      headers: createHeaders(cookie, options?.headers),
      ...(agent && { agent }),
      credentials: 'include',
    });

    if (res.ok) {
      return await parseResponse<T>(res);
    }

    throw res;
  } catch (error) {
    const res = error as Response;
    console.error('fetcher error: ', { url: res.url, status: res.status, statusText: res.statusText });

    if (res.status === HTTP_STATUS_CODE.UNAUTHORIZED) {
      // Server-side: Get current path from headers and redirect to logout API with redirect info
      try {
        const headersList = await headers();
        const currentUrl = headersList.get('x-url');

        if (currentUrl) {
          const url = new URL(currentUrl);
          const pathname = url.pathname;
          const query = url.search.slice(1); // Remove the '?' prefix
          const logoutUrl = `${NEXT_API_ENDPOINTS.LOGOUT}?pathname=${encodeURIComponent(pathname)}${query ? `&query=${encodeURIComponent(query)}` : ''}`;
          await redirectPath(logoutUrl);
        } else {
          await redirectPath(NEXT_API_ENDPOINTS.LOGOUT);
        }
      } catch (error) {
        // Fallback if headers are not available
        console.warn('Unable to get current URL from headers:', error);
        await redirectPath(NEXT_API_ENDPOINTS.LOGOUT);
      }
    }

    return {
      success: false,
      data: null,
      status: res.status,
      message: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}
