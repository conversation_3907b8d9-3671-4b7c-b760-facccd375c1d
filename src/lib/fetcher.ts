import { clearCookies, getCookiesAsString } from '@/actions/cookieAction';
import { redirectPath } from '@/actions/redirectPath';
import { routePaths } from '@/config';
import { HTTP_STATUS_CODE, NEXT_API_ENDPOINTS } from '@/constants/api';

interface ApiResponse<T> {
  success: boolean;
  data: T | null;
  status: number;
  message: string;
}

type FetchConfig = RequestInit & {
  // maxRetries?: number;
  // retryDelay?: (attempt: number) => number;
};

async function parseResponse<T>(res: Response): Promise<ApiResponse<T>> {
  const contentType = res.headers.get('content-type');
  if (contentType?.includes('application/json')) {
    const data = await res.json();
    return { success: res.ok, data, status: res.status, message: res.statusText };
  }

  return { success: res.ok, data: null, status: res.status, message: res.statusText };
}

const createHeaders = async (config: FetchConfig) => {
  const isClient = typeof window !== 'undefined';
  const isFormData = config.body instanceof FormData;

  const headersDefault: Record<string, string> = isFormData ? {} : { 'Content-Type': 'application/json' };

  if (isClient) {
    return { ...headersDefault, ...config.headers };
  }

  const cookie = await getCookiesAsString();
  return { ...headersDefault, ...config.headers, ...(cookie && { Cookie: cookie }) };
};

export async function fetcher<T>(url: string, config: FetchConfig = {}): Promise<ApiResponse<T>> {
  const headers = await createHeaders(config);

  const options = { ...config, headers, credentials: 'include' } satisfies RequestInit;

  try {
    const res = await fetch(url, options);

    if (res.ok) {
      const parsedData = await parseResponse<T>(res);
      return parsedData;
    }

    throw res;
  } catch (error) {
    const res = error as Response;
    console.error('fetcher error: ', { url: res.url, status: res.status, statusText: res.statusText });

    if (res.status === HTTP_STATUS_CODE.UNAUTHORIZED) {
      if (typeof window !== 'undefined') {
        // Client-side: Clear cookies and redirect to login with current path
        await clearCookies();
        const currentPath = window.location.pathname;
        const currentQuery = window.location.search;
        const encodedRedirect = encodeURIComponent(`${currentPath}${currentQuery}`);
        const redirectUrl = `${window.location.origin}${routePaths.login}?redirect=${encodedRedirect}`;
        location.href = redirectUrl;
      } else {
        // Server-side: Get current path from headers and redirect to logout API
        try {
          // Dynamically import headers only in server context
          const { headers } = await import('next/headers');
          const headersList = await headers();
          const currentUrl = headersList.get('x-url');

          if (currentUrl) {
            const url = new URL(currentUrl);
            const pathname = url.pathname;
            const query = url.search.slice(1); // Remove the '?' prefix
            const logoutUrl = `${NEXT_API_ENDPOINTS.LOGOUT}?pathname=${encodeURIComponent(pathname)}${query ? `&query=${encodeURIComponent(query)}` : ''}`;
            await redirectPath(logoutUrl);
          } else {
            await redirectPath(NEXT_API_ENDPOINTS.LOGOUT);
          }
        } catch (error) {
          // Fallback if headers are not available (e.g., not in server context)
          console.warn('Unable to get current URL from headers:', error);
          await redirectPath(NEXT_API_ENDPOINTS.LOGOUT);
        }
      }
    }

    return Promise.reject({
      success: false,
      data: null,
      status: res.status,
      message: res.statusText || 'An unexpected error occurred',
    });
  }
}
