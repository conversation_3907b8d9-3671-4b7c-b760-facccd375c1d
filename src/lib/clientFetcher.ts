'use client';

import { clearCookies } from '@/actions/cookieAction';
import { HTTP_STATUS_CODE } from '@/constants/api';
import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

const DEFAULT_CONFIG: AxiosRequestConfig = {
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
};

class ApiClient {
  private static instance: ApiClient;
  private axiosInstance: AxiosInstance;

  private constructor() {
    this.axiosInstance = axios.create(DEFAULT_CONFIG);
    this.setupInterceptors();
  }

  public static getInstance(): ApiClient {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient();
    }
    return ApiClient.instance;
  }

  private setupInterceptors(): void {
    this.axiosInstance.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        return config;
      },
      (error: AxiosError) => {
        return Promise.reject(this.handleError(error));
      },
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error: AxiosError) => {
        return Promise.reject(this.handleError(error));
      },
    );
  }

  private async handleError(error: AxiosError): Promise<AxiosResponse> {
    if (error.response?.status === HTTP_STATUS_CODE.UNAUTHORIZED) {
      await clearCookies();
      // Redirect to login with current path as redirect parameter
      const currentPath = window.location.pathname;
      const currentQuery = window.location.search;
      const encodedRedirect = encodeURIComponent(`${currentPath}${currentQuery}`);
      location.href = `${window.location.origin}/login?redirect=${encodedRedirect}`;
      return { ...error.response };
    }

    return error.response as AxiosResponse;
  }

  public get client(): AxiosInstance {
    return this.axiosInstance;
  }
}

export const clientFetcher = ApiClient.getInstance().client;
