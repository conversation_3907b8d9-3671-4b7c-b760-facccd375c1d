'use client';
import HomeCourseItem from '@/components/home/<USER>';
import { CourseType } from '@/constants/enum';
import { transformSecondToHour } from '@/constants/time';
import { CourseInfo } from '@/features/courses';
import { Button, Col, Form, Row, Space } from 'antd';
import { useState } from 'react';
import { useFavoriteCourse } from '../hooks';
import { Topic } from '../types/course.type';
import CourseFilterForm from './CourseFilterForm';
import CourseFilterModal from './CourseFilterModal';
import CoursesPagination from './CoursePagination';
import CourseTopics from './CourseTopics';

const FilterIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="size-6"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-9.75 0h9.75"
      />
    </svg>
  );
};

const CoursesManagement = ({
  coursesData,
  isLoggedIn,
  topics = [],
  showFilter = true,
}: {
  topics?: Topic[];
  coursesData: { data: CourseInfo[]; count: number };
  isLoggedIn: boolean;
  showFilter?: boolean;
}) => {
  const { count: total, data: courses } = coursesData;

  const collapsed = true;

  const { isUpdatingFavorite, onFavorite } = useFavoriteCourse();

  const [form] = Form.useForm();

  const [openFilterModal, setOpenFilterModal] = useState(false);

  const allTopics = [{ id: '', topicName: 'Tất cả khóa học' }].concat(topics) as Topic[];

  return (
    <div className={'bg-white'}>
      <Space className="w-full" direction={'vertical'} size={24}>
        {showFilter && (
          <>
            <div className="text-2xl font-semibold text-ink-black">Tất cả khóa học</div>
            <div className="flex gap-12">
              <div style={{ width: `calc(100vw - ${collapsed ? '435' : '734'}px)` }}>
                <CourseTopics topics={allTopics} />
              </div>
              <div>
                <Button onClick={() => setOpenFilterModal(true)}>
                  <Space>
                    <div>Bộ lọc</div>
                    <FilterIcon />
                  </Space>
                </Button>
              </div>
            </div>
          </>
        )}
        <Row gutter={[24, 24]}>
          {courses?.length > 0 ? (
            courses.map((item) => (
              <Col key={item.id} md={8} lg={8} xxl={6} span={24}>
                <HomeCourseItem
                  authorName={item?.createdBy?.name || ''}
                  isFavorite={!!item?.isFavorite}
                  totalRate={Number(item.totalRating) ?? 0}
                  category={item.topic?.topicName ?? ''}
                  course_description={item.courseDescription}
                  avg_rating={Number(item.avgRating) ?? 0} // DEPRECATED
                  duration={transformSecondToHour(Number(item.courseDuration)) ?? 0}
                  totalLearner={item?.totalLearner ?? 0}
                  course_thumbnail_image={item.courseThumbnailImage ?? ''}
                  avgRate={Number(item.avgRating) ?? 0}
                  course_name={item.courseName}
                  sections_count={item.totalSections ?? 0}
                  id={item.id}
                  isMajor={false}
                  isLiking={isUpdatingFavorite}
                  course_level_id={Number(item.courseLevelId)}
                  isShortCourse={item.courseTypeId === CourseType.Short}
                  isLoggedIn={isLoggedIn}
                  onLike={(isFavorite: boolean) => onFavorite({ courseId: item.id, isFavorite })}
                />
              </Col>
            ))
          ) : (
            <Col span={24}>
              <p className={'text-center font-bold'}>Không tìm thấy khóa học phù hợp với điều kiện</p>
            </Col>
          )}
        </Row>
        {courses.length > 0 && <CoursesPagination total={total} />}
      </Space>

      {openFilterModal && (
        <CourseFilterModal open={openFilterModal} setOpen={setOpenFilterModal} form={form}>
          <CourseFilterForm form={form} />
        </CourseFilterModal>
      )}
    </div>
  );
};

export default CoursesManagement;
