# Redirect URL Implementation Test Guide

This document outlines how to test the redirect URL functionality when tokens expire.

## Implementation Summary

The redirect URL functionality has been implemented across all authentication layers:

### 1. Client-side Token Expiration (Browser)
- **Files Updated**: `src/lib/fetcher.ts`, `src/lib/clientFetcher.ts`, `src/hooks/useAxios.ts`
- **Behavior**: When a 401 error occurs on the client side, the user is redirected to `/login?redirect=<encoded-current-path>`

### 2. Server-side Token Expiration
- **Files Updated**: `src/lib/fetcher.ts`, `src/lib/serverFetcher.ts`
- **Behavior**: When a 401 error occurs on the server side, the user is redirected to `/api/auth/logout?pathname=<path>&query=<query>` which then redirects to login with the redirect parameter

### 3. Middleware Protection
- **File**: `src/middleware.ts` (already implemented)
- **Behavior**: Unauthenticated users accessing protected routes are redirected to `/login?redirect=<encoded-path>`

### 4. Login Flow
- **Files**: `src/hooks/useAuth.tsx` (already implemented)
- **Behavior**: After successful login, users are redirected to the original page they were trying to access

## Testing Steps

### Test 1: Middleware Redirect (Unauthenticated User)
1. Open browser in incognito mode
2. Navigate to `http://localhost:3000/profile`
3. **Expected**: Redirect to `/login?redirect=%2Fprofile`
4. Login with valid credentials
5. **Expected**: Redirect back to `/profile`

### Test 2: Client-side Token Expiration
1. Login to the application
2. Open browser developer tools
3. Go to Application/Storage tab and delete the `accessToken` cookie
4. Navigate to any page that makes API calls (e.g., `/profile`)
5. **Expected**: Redirect to `/login?redirect=<current-path>`
6. Login again
7. **Expected**: Redirect back to the original page

### Test 3: Server-side Token Expiration
1. Login to the application
2. Navigate to a server-rendered page that fetches data (e.g., `/profile`)
3. Manually expire or delete authentication cookies on the server
4. Refresh the page
5. **Expected**: Redirect to `/login?redirect=<current-path>`

### Test 4: Complex URL with Query Parameters
1. Navigate to a URL with query parameters (e.g., `/profile/courses?tab=newest&page=2`)
2. Trigger token expiration (delete cookies)
3. **Expected**: Redirect to `/login?redirect=%2Fprofile%2Fcourses%3Ftab%3Dnewest%26page%3D2`
4. Login
5. **Expected**: Redirect back to `/profile/courses?tab=newest&page=2`

## Key Implementation Details

### URL Encoding
- Current path and query parameters are properly encoded using `encodeURIComponent()`
- Decoded on login using `decodeURIComponent()`

### Error Handling
- Fallback to basic logout redirect if URL extraction fails
- Console warnings for debugging server-side issues

### Multiple Fetcher Support
- `fetcher.ts`: Main fetch wrapper (client + server)
- `serverFetcher.ts`: Server-only fetch wrapper
- `clientFetcher.ts`: Axios-based client wrapper
- `useAxios.ts`: React hook with Axios interceptors

### Security Considerations
- Only redirects to internal paths (same origin)
- Proper cookie clearing on token expiration
- Server-side validation of authentication state

## Files Modified

1. `src/lib/fetcher.ts` - Main fetcher with client/server redirect logic
2. `src/lib/serverFetcher.ts` - Server-side fetcher with redirect logic
3. `src/lib/clientFetcher.ts` - Client-side Axios wrapper with redirect logic
4. `src/hooks/useAxios.ts` - Axios hook with redirect logic
5. `src/app/api/auth/logout/route.ts` - Already supported redirect parameters
6. `src/hooks/useAuth.tsx` - Already handled redirect after login
7. `src/middleware.ts` - Already handled unauthenticated redirects

## Expected User Experience

1. **Seamless Flow**: Users are automatically redirected back to their intended page after login
2. **No Data Loss**: Query parameters and complex URLs are preserved
3. **Consistent Behavior**: Same redirect logic across all authentication scenarios
4. **Error Recovery**: Graceful fallback if redirect URL cannot be determined
