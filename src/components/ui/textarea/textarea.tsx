import { cn } from '@/lib/utils';
import { Input } from 'antd';
import { TextAreaRef } from 'antd/es/input/TextArea';
import clsx from 'clsx';
import React from 'react';
import './textarea.scss';

type TextAreaProps = {
  size?: 'sm' | 'md' | 'lg';
  helperText?: React.ReactNode;
} & React.ComponentProps<typeof Input.TextArea>;

const sizeStyle = {
  sm: clsx('textarea-size-sm h-[42px] text-body-sm'),
  md: clsx('textarea-size-md h-[50px] text-body-md'),
  lg: clsx('textarea-size-lg h-[64px] text-body-lg'),
};

const TextArea = React.forwardRef<TextAreaRef, TextAreaProps>((props, ref) => {
  const { className, size = 'md', helperText, disabled = false, ...rest } = props;

  return (
    <React.Fragment>
      <Input.TextArea
        ref={ref}
        className={clsx('text-area-custom w-96', sizeStyle[size], cn(className, !disabled && 'bg-white'))}
        showCount
        count={{ max: 100 }}
        disabled={disabled}
        {...rest}
      />

      {rest.status === 'error' && <div className="text-body-sm text-red-500">{helperText}</div>}
    </React.Fragment>
  );
});

TextArea.displayName = 'TextArea';

export default TextArea;
