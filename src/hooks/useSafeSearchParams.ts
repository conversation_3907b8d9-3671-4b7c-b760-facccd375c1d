'use client';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import queryString from 'query-string';

const useSafeSearchParams = <T>() => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const parsedQueryParams = queryString.parse(searchParams.toString()) as T;

  const setSearchParams = (params: Partial<T>, options: { replace?: boolean } = {}) => {
    const newQuery = {
      ...parsedQueryParams,
      ...params,
    };

    const queryStringified = queryString.stringify(newQuery, {
      skipNull: true,
      skipEmptyString: true,
    });

    const url = `${pathname}${queryStringified ? `?${queryStringified}` : ''}`;

    if (options.replace) {
      router.replace(url);
    } else {
      router.push(url);
    }
  };

  const getSearchParams = <K extends keyof T>(key: K): T[K] | undefined => {
    const value = searchParams.get(key as string);
    return value === null ? undefined : (value as T[K]);
  };

  return { searchParams, parsedQueryParams, getSearchParams, setSearchParams };
};

export default useSafeSearchParams;
