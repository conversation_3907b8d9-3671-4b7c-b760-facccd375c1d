'use client';

import { S3MultipartUploader } from '@/lib/s3-uploader';
import React from 'react';

const useS3Uploader = () => {
  const [isUploading, setIsUploading] = React.useState<boolean>(false);
  const [progress, setProgress] = React.useState<number>(0);

  const uploadFile = async <T = unknown,>(
    { file, fileDuration }: { file: File; fileDuration?: number },
    options?: { onSuccess?: (uploadedRes: T) => void; onError?: (error: unknown) => void },
  ) => {
    const { onSuccess, onError } = options ?? {};

    try {
      setIsUploading(true);
      const s3Uploader = new S3MultipartUploader(file);
      const uploadedRes = await s3Uploader.uploadFile({
        fileDuration,
        onProgress: (progress) => setProgress(progress),
      });

      onSuccess?.(uploadedRes as T);
    } catch (error) {
      onError?.(error);
    } finally {
      setIsUploading(false);
    }
  };

  return { isUploading, progress, uploadFile };
};

export default useS3Uploader;
