import { Icon } from '@/components/client';
import { Typography } from '@/components/ui';
import { cn } from '@/lib/utils';
import { CheckIcon, XCircleIcon, XMarkIcon } from '@heroicons/react/16/solid';
import { App } from 'antd';

export const useNotification = () => {
  const { notification } = App.useApp();

  const success = ({ message, description }: { message: string; description?: string }) => {
    notification.open({
      message: <Typography variant="labelMd">{message}</Typography>,
      description: <Typography variant="labelMd">{description}</Typography>,
      icon: <Icon icon={<CheckIcon className="rounded-full bg-green-500 text-white" />} />,
      className: cn('m-0 flex items-center rounded-s border border-green-500 bg-green-50 p-4'),
      closeIcon: <Icon icon={<XMarkIcon className="size-5" />} />,
    });
  };

  const error = ({ message, description }: { message: string; description?: string }) => {
    notification.open({
      message: <Typography variant="labelMd">{message}</Typography>,
      description: <Typography variant="labelMd">{description}</Typography>,
      icon: <Icon icon={<XCircleIcon className="rounded-full bg-red-500 text-white" />} />,
      className: cn('flex items-center rounded-s border border-red-500 bg-red-50 p-4'),
      closeIcon: <Icon icon={<XMarkIcon className="size-5" />} />,
      duration: 500,
    });
  };

  return { success, error };
};
