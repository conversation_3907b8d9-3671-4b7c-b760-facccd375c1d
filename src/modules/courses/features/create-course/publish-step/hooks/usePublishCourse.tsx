import { useNotification } from '@/hooks';
import { publishCourseService } from '@/modules/courses/services/course.service';
import { useParams } from 'next/navigation';
import React from 'react';
import { useMutation } from 'react-query';

const usePublishCourse = () => {
  const notification = useNotification();
  const params = useParams<{ courseId: string }>();

  const { mutate: publishCourse, isLoading } = useMutation({
    mutationFn: publishCourseService,
  });

  const [isPublishing, setIsPublishing] = React.useState(false);
  const [showSuccessModal, setShowSuccessModal] = React.useState(false);

  const handlePublish = () => setIsPublishing(true);
  const handleCancel = () => setIsPublishing(false);

  const handleConfirmPublish = () => {
    publishCourse(
      { courseId: params.courseId },
      {
        onSuccess: () => {
          setIsPublishing(false);
          setShowSuccessModal(true);
        },
        onError: () => notification.error({ message: '<PERSON><PERSON><PERSON> bản khóa học thất bại' }),
      },
    );
  };

  return {
    isPublishing,
    isLoading,
    showSuccessModal,

    onPublish: handlePublish,
    onCancel: handleCancel,
    onConfirmPublish: handleConfirmPublish,

    setShowSuccessModal,
  };
};

export default usePublishCourse;
