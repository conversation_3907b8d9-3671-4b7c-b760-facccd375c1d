'use client';

import { Button, Typography } from '@/components/ui';
import { Checkbox, Modal } from 'antd';
import Link from 'next/link';
import { useState } from 'react';

interface PublishConfirmModalProps {
  open: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  isLoading?: boolean;
}

export function PublishConfirmModal({ open, onCancel, onConfirm, isLoading = false }: PublishConfirmModalProps) {
  const [isTermsAccepted, setIsTermsAccepted] = useState(false);

  return (
    <Modal
      open={open}
      className="md:!w-[715px]"
      onClose={onCancel}
      title="Xác nhận xuất bản khoá học"
      footer={
        <div className="flex items-center justify-end gap-3 p-6 pt-4">
          <Button size="large" variant="tertiary" onClick={onCancel}>
            Tiế<PERSON> tục chỉnh sửa
          </Button>
          <Button onClick={onConfirm} size="large" disabled={!isTermsAccepted} loading={isLoading}>
            <PERSON><PERSON><PERSON> nhận
          </Button>
        </div>
      }
    >
      <div className="flex flex-col items-start gap-2 px-6 py-4">
        <Checkbox onChange={(e) => setIsTermsAccepted(e.target.checked)} checked={isTermsAccepted}>
          <Typography variant="labelMd">
            Tôi xác nhận và cam kết nội dung khóa học tôi muốn xuất bản tuân thủ theo{' '}
            <Link href="/documents/terms.pdf" target="_blank" rel="noopener noreferrer">
              Các Điều khoản và Điều kiện
            </Link>{' '}
            của nền tảng và{' '}
            <Link href="/documents/copyright-policy.pdf" target="_blank" rel="noopener noreferrer">
              Chính sách Quản lý bản quyền
            </Link>{' '}
            của Studify.
          </Typography>
        </Checkbox>
      </div>
    </Modal>
  );
}
