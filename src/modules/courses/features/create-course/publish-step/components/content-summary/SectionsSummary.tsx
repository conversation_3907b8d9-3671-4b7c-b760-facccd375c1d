import { Collapse, Typography } from '@/components/ui';
import { palette } from '@/config/theme';
import { Test } from '@/features/courses/types/test.type';
import DotIcon from '@/icons/DotIcon';
import { secondsToHHMMSSTextFormat } from '@/lib/dateTime';
import { cn } from '@/lib/utils';
import { ChapterType, LessonType } from '@/modules/courses/constants/course.const';
import { CourseInfo, Lecture, Section } from '@/modules/courses/types/course.type';
import { ChevronRightIcon, DocumentTextIcon, VideoCameraIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';

const LessonItem = ({ lesson }: { lesson: Lecture }) => {
  const renderLessonInfo = () => {
    const isVideoLesson = lesson.lectureType === LessonType.Video;

    const totalTestQuestions = lesson.test?.questions.length ?? 0;

    const commonClasses = cn('flex items-center gap-2');
    const divider = <div className="h-4 w-[2px] bg-neutral-200" />;

    return (
      <div className={commonClasses}>
        <div className={commonClasses}>
          {isVideoLesson ? <VideoCameraIcon className="size-5" /> : <DocumentTextIcon className="size-5" />}

          <Typography variant="titleSm" className="text-placeholder_text">
            {isVideoLesson ? 'Bài giảng video' : 'Bài ôn tập'}
          </Typography>
        </div>

        {divider}

        <div className="flex items-center gap-1">
          <Typography variant="titleSm" className="text-placeholder_text">
            {isVideoLesson ? 'Thời lượng:' : 'Số lượng câu hỏi:'}
          </Typography>
          <Typography variant="bodyMd" className="text-placeholder_text">
            {isVideoLesson ? secondsToHHMMSSTextFormat(lesson.duration ?? 0) : totalTestQuestions}
          </Typography>
        </div>
      </div>
    );
  };

  return (
    <div
      className={cn('flex items-center justify-between py-4 pl-6 pr-5', 'border-b border-neutral-100 last:border-b-0')}
    >
      <div className="flex h-fit flex-col gap-1">
        <Typography variant="titleMd">{lesson.lectureName}</Typography>
        {renderLessonInfo()}
      </div>

      {!!lesson.lectureThumbnailImage && (
        <Image
          src={lesson.lectureThumbnailImage}
          alt="course-thumbnail"
          width={136}
          height={77}
          className="aspect-[16/9] rounded-md object-cover"
        />
      )}
    </div>
  );
};

const TestItem = ({ test }: { test: Test | null }) => {
  const totalQuestions = test?.questions.length ?? 0;

  return (
    <div className="flex items-center justify-between py-4 pl-6 pr-5">
      <div className="flex h-fit flex-col gap-1">
        <Typography variant="titleMd">Bài kiểm tra cuối khóa</Typography>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <DocumentTextIcon className="size-5" />
            <Typography variant="titleSm" className="text-placeholder_text">
              Bài kiểm tra
            </Typography>
          </div>
          <div className="h-4 w-[2px] bg-neutral-200" />
          <div className="flex items-center gap-1">
            <Typography className="text-placeholder_text" variant="titleSm">
              Số lượng câu hỏi:
            </Typography>
            <Typography className="text-placeholder_text">{totalQuestions}</Typography>
          </div>
        </div>
      </div>
    </div>
  );
};

const LectureList = ({ section, lectures }: { section: Section; lectures: Lecture[] }) => {
  const sectionType = section.sectionType;

  const classes = cn('-mx-5 -my-4 bg-neutral-50');

  if (sectionType === ChapterType.Test) {
    return (
      <div className={cn(classes)}>
        <TestItem test={section.test} key={section.id} />
      </div>
    );
  }

  return (
    <div className={cn(classes)}>
      {lectures.map((lesson: Lecture) => {
        return <LessonItem lesson={lesson} key={lesson.id} />;
      })}
    </div>
  );
};

const getCollapseSection = (section: Section) => {
  const totalLessons = section.lectures.length;
  const totalLecturesDuration = section.lectures.reduce((total, lesson) => total + (lesson.duration ?? 0), 0);

  return {
    id: section.id,
    classNames: {
      header: cn('px-5 py-4'),
    },
    label: (
      <div className="flex justify-between bg-white">
        <Typography variant="titleMd" className="w-3/4 truncate" title={section.sectionName}>
          {section.sectionName}
        </Typography>

        {section.sectionType === ChapterType.Default && (
          <div className="flex items-center gap-1">
            <Typography variant="bodyLg" className="text-secondary_text">
              {totalLessons} bài giảng
            </Typography>

            <DotIcon height={8} width={8} fill={palette.text.secondary_text} />

            <Typography variant="bodyLg" className="text-secondary_text">
              {secondsToHHMMSSTextFormat(totalLecturesDuration)}
            </Typography>
          </div>
        )}
      </div>
    ),
    children: <LectureList section={section} lectures={section.lectures} />,
  };
};

export default function SectionsSummary({ courseDetail }: { courseDetail: CourseInfo }) {
  const sections = courseDetail.sections || [];
  const lessons = sections.filter((section) => section.sectionType === ChapterType.Default);
  const tests = sections.filter((section) => section.sectionType === ChapterType.Test);
  const finalList = [...lessons, ...tests];

  return (
    <div className="flex flex-col gap-4">
      {finalList.map((section) => {
        const collapseItems = getCollapseSection(section);
        return (
          <Collapse
            key={section.id}
            expandIcon={({ isActive }) => <ChevronRightIcon className={`size-6 ${isActive ? 'rotate-90' : ''}`} />}
            className="rounded-md bg-white"
            items={[collapseItems]}
          />
        );
      })}
    </div>
  );
}
