'use client';

import { HolderOutlined } from '@ant-design/icons';

import { Icon } from '@/components/client/icon';
import { Button, Collapse, CollapseProps, Input, Typography } from '@/components/ui';
import { cn } from '@/lib/utils';
import { LibraryModal } from '@/modules/courses/components';
import { LibraryFileType } from '@/modules/courses/constants/file.const';
import { useTypedFormContext } from '@/modules/courses/features/create-course/design-step/quiz/useTypedFormContext';
import { ChevronDownIcon, PhotoIcon, TrashIcon, VideoCameraIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';
import React from 'react';
import { Controller, useWatch } from 'react-hook-form';
import ReactPlayer from 'react-player';

type QuestionFormItemProps = {
  index: number;
  actions: React.ReactNode;
  optionsContent: React.ReactNode;
};

export default function QuestionFormItem(props: QuestionFormItemProps) {
  const { index, actions, optionsContent } = props;

  const [isExpanded, setIsExpanded] = React.useState(true);
  const [openLibraryModal, setOpenLibraryModal] = React.useState<'image' | 'video' | null>(null);

  const { control, setValue, trigger } = useTypedFormContext();

  const questionWatched = useWatch({ control, name: `questions.${index}` });
  const questionTitle = `Câu hỏi ${index + 1}`;

  const handleExpandCollapse = () => {
    setIsExpanded((prevKey) => !prevKey);
  };

  const handleOpenLibraryModal = (type: 'image' | 'video') => {
    setOpenLibraryModal(type);
  };

  const handleSetQuestionImage = (file: { fileUrl: string; fileId: string } | null) => {
    setValue(`questions.${index}.questionThumbnailImage`, file, { shouldDirty: true });
    trigger(`questions.${index}.questionThumbnailImage`);
  };

  const handleSetQuestionVideo = (file: { fileUrl: string; fileId: string } | null) => {
    setValue(`questions.${index}.questionVideoUrl`, file, { shouldDirty: true });
    trigger(`questions.${index}.questionThumbnailImage`);
  };

  const items = [
    {
      showArrow: false,
      classNames: { header: cn('p-0'), body: cn('p-0') },
      key: index,
      label: (
        <div className="flex w-full cursor-pointer flex-col gap-3 p-6" onClick={handleExpandCollapse}>
          <div className="flex items-center justify-between">
            <Typography variant="headlineXs">{questionTitle}</Typography>
            <ChevronDownIcon className={cn('size-6 text-primary_text', { 'rotate-180': isExpanded })} />
          </div>
        </div>
      ),
      children: (
        <div className="flex flex-col gap-4">
          <div className="flex w-full flex-col gap-4 px-6">
            <div className="flex gap-1">
              <Controller
                name={`questions.${index}.questionName`}
                control={control}
                render={({ field, formState }) => {
                  const error = formState.errors.questions?.[index]?.questionName;
                  return (
                    <div className="w-full">
                      <Input
                        {...field}
                        placeholder="Nhập câu hỏi"
                        className={cn(
                          'w-full overflow-y-hidden rounded-none border-x-0 border-b border-t-0 border-b-neutral-200 bg-white p-0',
                          'focus-within:ring-0 focus:border-x-0 focus:border-t-0 focus:ring-0 focus:ring-white focus:ring-offset-0',
                        )}
                        status={error ? 'error' : undefined}
                      />
                      {error && <div className="mt-1 text-sm text-red-500">{error.message}</div>}
                    </div>
                  );
                }}
              />

              <Button
                variant="ghost"
                size="small"
                onClick={() => handleOpenLibraryModal('image')}
                className="text-primary_text hover:bg-neutral-50 hover:text-primary_text active:bg-neutral-50 active:text-primary_text"
              >
                <Icon icon={<PhotoIcon className="size-6" />} />
              </Button>

              <Button
                variant="ghost"
                size="small"
                onClick={() => handleOpenLibraryModal('video')}
                className="text-primary_text hover:bg-neutral-50 hover:text-primary_text active:bg-neutral-50 active:text-primary_text"
              >
                <Icon icon={<VideoCameraIcon className="size-6" />} />
              </Button>
            </div>

            <div className="flex gap-4">
              {questionWatched.questionThumbnailImage && (
                <div className="relative">
                  <Image
                    src={questionWatched.questionThumbnailImage.fileUrl}
                    alt="Question"
                    className="h-[200px] w-[300px] rounded object-cover"
                    width={100}
                    height={100}
                  />
                  <Button
                    variant="ghost"
                    size="small"
                    onClick={() => handleSetQuestionImage(null)}
                    className="absolute right-1 top-1 inline h-auto text-black"
                  >
                    <Icon icon={<TrashIcon />} />
                  </Button>
                </div>
              )}

              {questionWatched.questionVideoUrl && (
                <div className="relative">
                  <ReactPlayer
                    url={questionWatched.questionVideoUrl.fileUrl}
                    playing={false}
                    muted
                    height={200}
                    width={300}
                    className={cn('[&>video]:rounded-lg [&>video]:object-cover')}
                  />
                  <Button
                    variant="ghost"
                    size="small"
                    className="absolute right-1 top-1 h-auto text-primary_text"
                    onClick={() => handleSetQuestionVideo(null)}
                  >
                    <Icon icon={<TrashIcon />} />
                  </Button>
                </div>
              )}
            </div>
          </div>

          <div className="px-6">{optionsContent}</div>

          <div className="border-t border-neutral-200 p-6">{actions}</div>
        </div>
      ),
    },
  ] satisfies CollapseProps['items'];

  return (
    <React.Fragment>
      <div className="flex">
        <div className="size-fit cursor-move pr-2 pt-8">
          <HolderOutlined className="text-xl" />
        </div>

        <div className="w-full rounded-lg border border-neutral-200 bg-white">
          <Collapse
            defaultActiveKey={[index]}
            collapsible="icon"
            ghost
            activeKey={isExpanded ? [index] : []}
            items={items}
          />
        </div>
      </div>

      {openLibraryModal && (
        <LibraryModal
          title={`Chọn ${openLibraryModal === 'image' ? 'hình ảnh' : 'video'}`}
          open={!!openLibraryModal}
          enabledTypes={[openLibraryModal === 'image' ? LibraryFileType.IMAGE : LibraryFileType.VIDEO]}
          onAddFile={(file) => {
            if (openLibraryModal === 'image') {
              handleSetQuestionImage({ fileUrl: file.fileUrl, fileId: file.id });
              return;
            }

            handleSetQuestionVideo({ fileUrl: file.fileUrl, fileId: file.id });
          }}
          onClose={() => setOpenLibraryModal(null)}
        />
      )}
    </React.Fragment>
  );
}
