'use client';

import { useNotification } from '@/hooks';
import useTestActions from '@/modules/courses/features/create-course/design-step/hooks/useTestActions';
import { QuizFormData } from '@/modules/courses/features/create-course/design-step/quiz/quiz-form.type';
import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import QuestionForm from './QuestionForm';
import { getQuizDefaultValues, getQuizPayloadRequest, quizSchema } from './quiz.schema';
import QuizHeader from './QuizHeader';
import QuizSettings from './QuizSettings';

const useQuizForm = () => {
  const notification = useNotification();
  const { onUpdateQuiz, testDetail } = useTestActions();

  const formMethods = useForm({ mode: 'all', resolver: zodResolver(quizSchema) });

  const handleSaveTest = (values: QuizFormData) => {
    if (!testDetail) return;

    const payload = getQuizPayloadRequest(values, testDetail);
    onUpdateQuiz(payload, {
      onSuccess: () => {
        notification.success({ message: 'Lưu bài ôn tập thành công' });
      },
      onError: () => {
        notification.error({ message: 'Lưu bài ôn tập thất bại' });
      },
    });
    formMethods.reset(values);
  };

  React.useEffect(() => {
    if (testDetail) {
      const defaultValues = getQuizDefaultValues(testDetail);
      formMethods.reset(defaultValues);
    }
  }, [testDetail]);

  return {
    formMethods,
    onSaveTest: handleSaveTest,
  };
};

export default function QuizContainer() {
  const { formMethods, onSaveTest } = useQuizForm();

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={formMethods.handleSubmit(onSaveTest)} className="h-full">
        <div className="flex h-full flex-col gap-4 overflow-y-auto px-8 py-10">
          <QuizHeader />

          <div className="flex h-full min-h-fit gap-6">
            <QuestionForm />
            <QuizSettings />
          </div>
        </div>
      </form>
    </FormProvider>
  );
}
