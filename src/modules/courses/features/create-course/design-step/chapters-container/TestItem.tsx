import { Icon } from '@/components/client';
import { Button, Typography } from '@/components/ui';
import { cn } from '@/lib/utils';
import SectionTitle from '@/modules/courses/features/create-course/design-step/chapters-container/SectionTitle';
import DeleteConfirmModal from '@/modules/courses/features/create-course/design-step/components/DeleteConfirmModal';
import useSectionActions from '@/modules/courses/features/create-course/design-step/hooks/useSectionActions';
import { Section } from '@/modules/courses/types/course.type';
import { DocumentTextIcon, TrashIcon } from '@heroicons/react/24/outline';
import { Tooltip } from 'antd';
import { useParams } from 'next/navigation';
import React from 'react';

type Props = {
  section: Section;
};

function TestInfo() {
  return (
    <div className="flex w-full gap-2">
      <Icon icon={<DocumentTextIcon />} className="size-5" />
      <Typography className="text-placeholder_text" variant="titleSm">
        Bài kiểm tra
      </Typography>
      <div>
        <div className="h-full border-l border-neutral-200" />
      </div>
      <div className="flex gap-2">
        <Typography className="text-placeholder_text" variant="titleSm">
          Số lượng câu hỏi:
        </Typography>
        <Typography className="text-placeholder_text" variant="bodyMd">
          0
        </Typography>
      </div>
    </div>
  );
}

const TestItem = (props: Props) => {
  const { section } = props;

  const { courseId } = useParams<{ courseId: string }>();

  const { onDeleteSection, onEditSection } = useSectionActions();

  const [isOpenDeleteModal, setIsOpenDeleteModal] = React.useState(false);

  const handleConfirmDelete = () => {
    onDeleteSection({ courseId, sectionId: section.id });
    setIsOpenDeleteModal(false);
  };

  const handleConfirm = (sectionName: string) => {
    onEditSection({ sectionName, sectionId: section.id, courseId });
  };

  return (
    <div>
      <div className={cn('flex w-full flex-col justify-between rounded-lg border border-neutral-200 bg-white')}>
        <div className={cn('relative flex w-full justify-between py-4 pl-4 pr-2')}>
          <SectionTitle
            value={section.sectionName}
            type={section.sectionType}
            onConfirm={handleConfirm}
            onCancel={() => setIsOpenDeleteModal(false)}
          />
        </div>

        <div className="h-full border-t border-neutral-200">
          <div className="py-4 pl-10 pr-2">
            <div className="flex items-center justify-between">
              <div className="flex flex-col gap-2">
                <Typography variant="titleMd">Bài kiểm tra cuối khóa</Typography>
                <TestInfo />
              </div>

              <Tooltip title={'Xóa bài kiểm tra'}>
                <Button
                  disabled={section.id === 'draft'}
                  variant="tertiary"
                  className="p-2.5"
                  size="small"
                  onClick={() => setIsOpenDeleteModal(true)}
                >
                  <Icon icon={<TrashIcon />} className="size-5" />
                </Button>
              </Tooltip>
            </div>
          </div>
        </div>

        {isOpenDeleteModal && (
          <DeleteConfirmModal
            type="FINAL_TEST"
            open={isOpenDeleteModal}
            onClose={() => setIsOpenDeleteModal(false)}
            onCancel={() => setIsOpenDeleteModal(false)}
            onConfirm={handleConfirmDelete}
          />
        )}
      </div>
    </div>
  );
};

export default TestItem;
