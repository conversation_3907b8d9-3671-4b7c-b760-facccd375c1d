import { Icon } from '@/components/client';
import { Button, Typography } from '@/components/ui';
import { QUERY_KEYS } from '@/constants/query-keys';
import { useNotification } from '@/hooks';
import useSafeSearchParams from '@/hooks/useSafeSearchParams';
import EditContentIcon from '@/icons/EditContentIcon';
import { secondsToHHMMSSTextFormat } from '@/lib/dateTime';
import { cn } from '@/lib/utils';
import { ChapterType, LessonType } from '@/modules/courses/constants/course.const';
import { useChaptersProvider } from '@/modules/courses/features/create-course/design-step/chapters-container/ChaptersProvider';
import DeleteConfirmModal from '@/modules/courses/features/create-course/design-step/components/DeleteConfirmModal';
import { createLessonService, deleteLessonService, editLessonService } from '@/modules/courses/services/course.service';
import { Lecture, Section } from '@/modules/courses/types/course.type';
import { HolderOutlined } from '@ant-design/icons';
import { DocumentTextIcon, TrashIcon, VideoCameraIcon } from '@heroicons/react/24/outline';
import { Tooltip } from 'antd';
import { useParams } from 'next/navigation';
import React from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { match } from 'ts-pattern';
import SectionTitle from './SectionTitle';

function LessonInfo(props: { type: LessonType; duration: number }) {
  const { type, duration } = props;

  if (type === 'VIDEO') {
    return (
      <div className="flex w-full gap-2 px-2">
        <Icon icon={<VideoCameraIcon />} className="size-5" />
        <Typography className="text-placeholder_text" variant="titleSm">
          Bài học Video
        </Typography>
        <div>
          <div className="h-full border-l border-neutral-200" />
        </div>

        <div className="flex gap-2">
          <Typography className="text-placeholder_text" variant="titleSm">
            Thời lượng:
          </Typography>
          <Typography className="text-placeholder_text" variant="bodyMd">
            {secondsToHHMMSSTextFormat(duration)}
          </Typography>
        </div>
      </div>
    );
  }
  return (
    <div className="flex w-full gap-2 px-2">
      <Icon icon={<DocumentTextIcon />} className="size-5" />
      <Typography className="text-placeholder_text" variant="titleSm">
        Bài ôn tập
      </Typography>
      <div>
        <div className="h-full border-l border-neutral-200" />
      </div>
      <div className="flex gap-2">
        <Typography className="text-placeholder_text" variant="titleSm">
          Số lượng câu hỏi:
        </Typography>
        <Typography className="text-placeholder_text" variant="bodyMd">
          0
        </Typography>
      </div>
    </div>
  );
}

const getLessonDefaultValue = (type: LessonType) => {
  return match(type)
    .with(LessonType.Video, () => 'Bài học')
    .with(LessonType.Test, () => 'Bài ôn tập')
    .exhaustive();
};

export function useLessonActions() {
  const notification = useNotification();
  const queryClient = useQueryClient();

  const createLessonMutation = useMutation({
    mutationFn: createLessonService,
    onSuccess: () => {
      queryClient.invalidateQueries([QUERY_KEYS.COURSE_DETAIL]);
    },
    onError: () => {
      notification.error({ message: 'Tạo bài học thất bại' });
    },
  });

  const editLessonMutation = useMutation({
    mutationFn: editLessonService,
    onSuccess: () => {
      queryClient.invalidateQueries([QUERY_KEYS.COURSE_DETAIL]);
    },
    onError: () => {
      notification.error({ message: 'Chỉnh sửa bài học thất bại' });
    },
  });

  const deleteLessonMutation = useMutation({
    mutationFn: deleteLessonService,
    onSuccess: () => {
      queryClient.invalidateQueries([QUERY_KEYS.COURSE_DETAIL]);
    },
    onError: () => {
      notification.error({ message: 'Xóa bài học thất bại' });
    },
  });

  return {
    createLessonMutation,
    editLessonMutation,
    deleteLessonMutation,
  };
}

function LessonItem({
  lesson,
  section,
  chapterType,
  isDragging,
  isDraft = false,
}: {
  lesson: Lecture;
  section: Section;
  chapterType: ChapterType;
  isDragging: boolean;
  isDraft?: boolean;
}) {
  const params = useParams<{ courseId: string }>();
  const { courseId } = params;

  const { setSearchParams } = useSafeSearchParams();

  const [isEdit, setIsEdit] = React.useState(false);
  const [isOpenDeleteModal, setIsOpenDeleteModal] = React.useState(false);
  const [lastCreateRequest, setLastCreateRequest] = React.useState<string | null>(null);
  const [lastEditRequest, setLastEditRequest] = React.useState<string | null>(null);

  const { sections, setSections, setIsCreatingDraft } = useChaptersProvider();
  const { createLessonMutation, editLessonMutation, deleteLessonMutation } = useLessonActions();

  const handleCreateLesson = (lessonName: string) => {
    if (createLessonMutation.isLoading || !isDraft) return;

    if (lastCreateRequest === lessonName && createLessonMutation.status === 'error') {
      return;
    }

    setLastCreateRequest(lessonName);
    createLessonMutation.mutate(
      {
        lectureType: lesson.lectureType,
        sortIndex: lesson.sortIndex,
        courseId: courseId,
        sectionId: section.id,
        lectureName: lessonName,
      },
      {
        onSuccess: () => {
          setIsCreatingDraft(false);
          setLastCreateRequest(null);
        },
      },
    );
  };

  const handleConfirmLessonInput = (lessonName: string) => {
    if (isEdit && lessonName !== lesson.lectureName) {
      if (lastEditRequest === lessonName && editLessonMutation.status === 'error') {
        return;
      }

      setLastEditRequest(lessonName);
      editLessonMutation.mutate(
        { courseId, sectionId: section.id, lectureId: lesson.id, lectureName: lessonName },
        {
          onSuccess: () => {
            setIsEdit(false);
            setLastEditRequest(null);
          },
        },
      );
      return;
    }

    if (isDraft) handleCreateLesson(lessonName);
  };

  const handleDeleteLesson = () => {
    deleteLessonMutation.mutate(
      { courseId, sectionId: section.id, lectureId: lesson.id },
      {
        onSuccess: () => {
          setIsOpenDeleteModal(false);
          setIsCreatingDraft(false);
        },
      },
    );
  };

  const handleCancelEdit = () => {
    if (isDraft) {
      const newSections = sections.map((sectionItem) => {
        if (sectionItem.id === section.id) {
          return { ...sectionItem, lectures: sectionItem.lectures.filter((lecture) => lecture.id !== 'draft') };
        }
        return sectionItem;
      }) as Section[];

      setSections(newSections);
      setIsCreatingDraft(false);
    }

    setIsEdit(false);
    setLastCreateRequest(null);
    setLastEditRequest(null);
  };

  const handleClickOutside = (value: string) => {
    if (isDraft) {
      const defaultName = getLessonDefaultValue(lesson.lectureType);
      const getUniqueLessonName = (baseIndex: number): string => {
        const name = `${defaultName} ${baseIndex}`;
        const isExisted = section.lectures.some((l) => l.lectureName === name && l.id !== lesson.id);
        if (isExisted) return getUniqueLessonName(baseIndex + 1);
        return name;
      };
      const finalLessonName = value?.trim()?.length ? value : getUniqueLessonName(lesson.sortIndex);
      handleCreateLesson(finalLessonName);
      return;
    }

    if (isEdit) handleConfirmLessonInput(value);
  };

  const handleEditVideoContent = () => {
    const searchParams =
      lesson.lectureType === LessonType.Video
        ? { sectionId: section.id, lessonId: lesson.id, testId: '' }
        : { sectionId: section.id, lessonId: lesson.id, testId: lesson.test?.id };
    setSearchParams(searchParams);
  };

  return (
    <React.Fragment>
      <div
        className={cn(
          'flex w-full flex-col justify-between gap-2 p-2 pl-4',
          isDragging && 'rounded-lg border border-neutral-200 bg-white p-2 shadow-md',
        )}
      >
        <div className="flex w-full items-center justify-between">
          <div className="flex w-full items-center gap-3">
            {chapterType === 'DEFAULT' ? (
              <div className="flex items-center justify-center">
                <HolderOutlined className="text-xl" />
              </div>
            ) : null}

            <SectionTitle
              editable={true}
              isCreatingDraft={isDraft}
              isEdit={isEdit}
              value={lesson.lectureName}
              type={lesson.lectureType}
              setIsEdit={setIsEdit}
              onConfirm={handleConfirmLessonInput}
              onCancel={handleCancelEdit}
              onClickOutside={(value) => {
                handleClickOutside(value);
              }}
            />
          </div>

          <div className="flex items-center justify-center gap-3">
            <Tooltip title="Chỉnh sửa nội dung bài">
              <Button variant="tertiary" className="p-2.5" size="small" onClick={handleEditVideoContent}>
                <Icon icon={<EditContentIcon />} className="size-5" />
              </Button>
            </Tooltip>

            <Tooltip title="Xóa bài">
              <Button variant="tertiary" className="p-2.5" size="small" onClick={() => setIsOpenDeleteModal(true)}>
                <Icon icon={<TrashIcon />} className="size-5" />
              </Button>
            </Tooltip>
          </div>
        </div>
        <div className="flex w-full flex-col justify-start gap-2 pl-8">
          <LessonInfo type={lesson.lectureType} duration={lesson.duration} />
        </div>
      </div>

      <DeleteConfirmModal
        open={isOpenDeleteModal}
        isDeleting={deleteLessonMutation.isLoading}
        type={lesson.lectureType === LessonType.Video ? 'LESSON' : 'QUIZ'}
        onCancel={() => setIsOpenDeleteModal(false)}
        onConfirm={handleDeleteLesson}
        onClose={() => setIsOpenDeleteModal(false)}
      />
    </React.Fragment>
  );
}

export default LessonItem;
