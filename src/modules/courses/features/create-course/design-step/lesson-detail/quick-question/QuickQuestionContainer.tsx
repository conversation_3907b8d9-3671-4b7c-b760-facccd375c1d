'use client';

import { Icon } from '@/components/client/icon';
import { <PERSON><PERSON>, <PERSON>, RichTextEditor, Typography } from '@/components/ui';
import { LibraryModal } from '@/modules/courses/components';
import { InteractionType } from '@/modules/courses/constants/course.const';
import { LibraryFileType } from '@/modules/courses/constants/file.const';
import { LibraryFile } from '@/modules/courses/types/file.type';
import { useLessonStore } from '@/z-store/lesson.store';
import { HolderOutlined } from '@ant-design/icons';
import { DragDropContext, Draggable, Droppable, DropResult } from '@hello-pangea/dnd';
import { Bars3Icon, PhotoIcon, PlusIcon, TrashIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { Input as AntInput, Checkbox, Radio } from 'antd';
import { useState } from 'react';
import { useQueryClient } from 'react-query';
import { QUERY_KEYS } from '../../../../../../../constants/query-keys';
import useInteractionAutoSave from '../../hooks/useInteractionAutoSave';

const { TextArea } = AntInput;

export default function QuickQuestionContainer() {
  const {
    interaction,
    updateQuestionName,
    updateQuestionImage,
    updateQuestionVideo,
    updateQuestionOptionText,
    updateQuestionOptionImage,
    addQuestionOption,
    removeQuestionOption,
    updateCorrectAnswer,
    reorderQuestionOptions,
    updateSettings,
  } = useLessonStore();

  // Auto-save functionality
  const { saveInteractionContent, saveWithCorrectAnswer, isSaving } = useInteractionAutoSave();

  // Extract question data from interaction (data is auto-updated by the hook)
  const isMultipleChoice = interaction.question.questionTypeId === 1;
  const correctAnswerArray = interaction.question.correctAnswer || [];
  const questionData = {
    question: interaction.question.questionName || '',
    questionImageUrl: interaction.question.questionImage || undefined,
    questionVideoUrl: interaction.question.videoUrl || undefined,
    answers: interaction.question.questionOptions || [],
    // For single choice, use the first answer as selected value (for Radio.Group)
    correctAnswerId: correctAnswerArray.length > 0 ? correctAnswerArray[0].toString() : '',
    // For multiple choice, use the array of indices
    correctAnswerIndices: correctAnswerArray,
  };
  const queryClient = useQueryClient();

  const [showLibraryModal, setShowLibraryModal] = useState(false);
  const [selectedAnswerForMedia, setSelectedAnswerForMedia] = useState<string>('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [answerToDelete, setAnswerToDelete] = useState<string>('');

  const handleQuestionChange = (value: string) => {
    if (!interaction.question) return;
    // Limit to 200 characters
    if (value.length > 200) return;
    updateQuestionName(value);
  };

  const handleQuestionBlur = () => {
    if (!interaction.question) return;
    saveInteractionContent();
  };

  const handleAnswerTextChange = (optionIndex: number, text: string) => {
    if (!interaction.question) return;
    // Limit to 200 characters
    updateQuestionOptionText(optionIndex, text);
  };

  const handleAnswerTextBlur = () => {
    if (!interaction.question) return;
    saveInteractionContent();
  };

  const handleCloseModal = () => {
    updateSettings({ isOpenInteractionType: InteractionType.Default });
    queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LECTURE_DETAIL] });
  };

  const handleCorrectAnswerChange = (e: any) => {
    if (!interaction.question) return;
    // Convert string ID to 0-based index for single choice
    const selectedIndex = parseInt(e.target.value);
    const newCorrectAnswer = [selectedIndex];
    updateCorrectAnswer(newCorrectAnswer);
    // Auto-save immediately with the new value
    saveWithCorrectAnswer(newCorrectAnswer);
  };

  const handleCheckboxChange = (optionIndex: number, checked: boolean) => {
    if (!interaction.question) return;
    const currentAnswers = questionData.correctAnswerIndices;
    const answerIndex = optionIndex;
    let newAnswers: number[];

    if (checked) {
      newAnswers = [...currentAnswers, answerIndex];
    } else {
      newAnswers = currentAnswers.filter((index) => index !== answerIndex);
    }

    updateCorrectAnswer(newAnswers);
    // Auto-save immediately with the new value
    saveWithCorrectAnswer(newAnswers);
  };

  const showDeleteConfirmation = (optionIndex: number) => {
    if (!interaction.question) return;
    setAnswerToDelete(optionIndex.toString());
    setShowDeleteModal(true);
  };

  const handleRemoveAnswer = () => {
    if (!interaction.question || !answerToDelete) return;
    removeQuestionOption(parseInt(answerToDelete));
    setShowDeleteModal(false);
    setAnswerToDelete('');
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setAnswerToDelete('');
  };

  const openMediaLibrary = (optionIndex: number | string) => {
    if (!interaction.question) return;
    setSelectedAnswerForMedia(optionIndex.toString());
    setShowLibraryModal(true);
  };

  const handleAddMedia = (file: LibraryFile) => {
    if (!interaction.question) return;
    if (selectedAnswerForMedia === 'question') {
      if (file.fileType === LibraryFileType.IMAGE) {
        updateQuestionImage(file.fileUrl);
        // Clear video if image is selected
        updateQuestionVideo('');
      } else if (file.fileType === LibraryFileType.VIDEO) {
        updateQuestionVideo(file.fileUrl);
        // Clear image if video is selected
        updateQuestionImage('');
      }
    } else {
      // For answer options, only images are supported currently
      if (file.fileType === LibraryFileType.IMAGE) {
        updateQuestionOptionImage(parseInt(selectedAnswerForMedia), file.fileUrl);
      }
    }
    setShowLibraryModal(false);
    setSelectedAnswerForMedia('');
    // Auto-save after media is added
    setTimeout(() => saveInteractionContent(), 0);
  };

  const handleRemoveQuestionMedia = () => {
    if (!interaction.question) return;
    updateQuestionImage('');
    updateQuestionVideo('');
    // Auto-save after media is removed
    setTimeout(() => saveInteractionContent(), 0);
  };

  const handleRemoveAnswerImage = (optionIndex: number) => {
    if (!interaction.question) return;
    updateQuestionOptionImage(optionIndex, '');
    // Auto-save after image is removed
    setTimeout(() => saveInteractionContent(), 0);
  };

  const onDragEnd = (result: DropResult) => {
    if (!interaction.question || !result.destination) return;
    reorderQuestionOptions(result.source.index, result.destination.index);
    // Auto-save after reordering
    setTimeout(() => saveInteractionContent(), 0);
  };

  return (
    <div className="absolute left-1/2 top-1/2 flex size-11/12 -translate-x-1/2 -translate-y-1/2 flex-col overflow-y-auto rounded-sm bg-white">
      {/* Fixed Question Section */}
      <div className="shrink-0 p-2">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="font-poppins text-xl font-semibold">Câu hỏi nhanh</h2>
          <div className="flex items-center gap-3">
            {isSaving && (
              <div className="flex items-center gap-2 text-sm text-neutral-500">
                <div className="size-4 animate-spin rounded-full border-2 border-neutral-300 border-r-transparent"></div>
                <span className="font-poppins">Đang lưu...</span>
              </div>
            )}
            <Button
              variant="ghost"
              size="small"
              onClick={handleCloseModal}
              className="text-gray-500 hover:text-gray-700 flex items-center"
            >
              <Icon icon={<XMarkIcon className="size-6" />} />
            </Button>
          </div>
        </div>

        <div className="mb-4 flex gap-1">
          <div className="flex-1">
            <TextArea
              placeholder="Nhập câu hỏi"
              value={questionData.question}
              onChange={(e) => handleQuestionChange(e.target.value)}
              onBlur={handleQuestionBlur}
              className="w-full overflow-y-hidden rounded-none border-x-0 border-b border-l-0 border-t-0 border-b-neutral-200 bg-white font-nunito"
              rows={1}
              autoSize
              classNames={{
                textarea: 'font-nunito',
              }}
              maxLength={200}
              showCount
            />
          </div>
          <Button
            variant="ghost"
            size="small"
            onClick={() => openMediaLibrary('question')}
            className="flex items-center gap-2 text-black"
          >
            <Icon icon={<PhotoIcon className="size-6" />} />
          </Button>
        </div>

        {questionData.questionImageUrl && (
          <div className="relative mb-4 inline-block">
            <img src={questionData.questionImageUrl} alt="Question" className="w-60 rounded object-cover" />
            <Button
              variant="ghost"
              size="small"
              onClick={handleRemoveQuestionMedia}
              className="absolute -right-2 -top-2 inline h-auto text-black"
            >
              <Icon icon={<TrashIcon className="size-6" />} />
            </Button>
          </div>
        )}

        {questionData.questionVideoUrl && (
          <div className="relative mb-4 inline-block">
            <video src={questionData.questionVideoUrl} className="w-60 rounded object-cover" controls />
            <Button
              variant="ghost"
              size="small"
              onClick={handleRemoveQuestionMedia}
              className="absolute -right-2 -top-2 inline h-auto text-black"
            >
              <Icon icon={<TrashIcon className="size-6" />} />
            </Button>
          </div>
        )}
      </div>

      {/* Scrollable Answers Section */}
      <div className="flex flex-1 flex-col">
        <div className="flex-1 space-y-3 overflow-y-auto">
          {isMultipleChoice ? (
            // Multiple choice: individual checkboxes
            <div className="w-full">
              <DragDropContext onDragEnd={onDragEnd}>
                <Droppable
                  droppableId="answers"
                  renderClone={(provided, snapshot, rubric) => {
                    const answer = questionData.answers[rubric.source.index];
                    return (
                      <div
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        ref={provided.innerRef}
                        className="flex items-center gap-3 rounded-lg bg-neutral-50 p-3"
                      >
                        <div className="cursor-move">
                          <Icon icon={<Bars3Icon className="size-4 text-neutral-400" />} />
                        </div>
                        <Checkbox
                          checked={questionData.correctAnswerIndices.includes(answer.option_index)}
                          onChange={(e) => handleCheckboxChange(answer.option_index, e.target.checked)}
                        />
                        <div className="flex-1">
                          <RichTextEditor
                            content={answer.option_name}
                            placeholder="Nhập đáp án"
                            className="pointer-events-none w-full"
                            maxLength={200}
                          />
                        </div>
                      </div>
                    );
                  }}
                >
                  {(provided) => (
                    <div {...provided.droppableProps} ref={provided.innerRef} className="relative space-y-3">
                      {questionData.answers.map((answer, index) => (
                        <Draggable key={answer.option_index} draggableId={answer.option_index.toString()} index={index}>
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className={`flex items-start gap-3 rounded-lg py-3 ${
                                snapshot.isDragging ? 'opacity-50' : 'bg-white'
                              }`}
                              style={provided.draggableProps.style}
                            >
                              <div {...provided.dragHandleProps} className="cursor-move">
                                <HolderOutlined className="text-xl" />
                              </div>

                              <Checkbox
                                checked={questionData.correctAnswerIndices.includes(answer.option_index)}
                                onChange={(e) => handleCheckboxChange(answer.option_index, e.target.checked)}
                              />

                              <div className="flex-1">
                                <RichTextEditor
                                  content={answer.option_name}
                                  placeholder="Nhập đáp án"
                                  onChange={(content) => handleAnswerTextChange(answer.option_index, content)}
                                  onBlur={handleAnswerTextBlur}
                                  className="w-full"
                                  maxLength={200}
                                />
                                {answer.option_thumbnail_image && (
                                  <div className="relative mt-2 inline-block">
                                    <img
                                      src={answer.option_thumbnail_image}
                                      alt="Answer"
                                      className="w-60 rounded object-cover"
                                    />
                                    <Button
                                      variant="ghost"
                                      size="small"
                                      onClick={() => handleRemoveAnswerImage(answer.option_index)}
                                      className="absolute -right-2 -top-2 inline h-auto text-black"
                                    >
                                      <Icon icon={<TrashIcon className="size-6" />} />
                                    </Button>
                                  </div>
                                )}
                              </div>

                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="small"
                                  onClick={() => openMediaLibrary(answer.option_index)}
                                  className="flex items-center text-black"
                                >
                                  <Icon icon={<PhotoIcon className="size-6" />} />
                                </Button>

                                <Button
                                  variant="ghost"
                                  size="small"
                                  onClick={() => showDeleteConfirmation(answer.option_index)}
                                  className="flex items-center text-black"
                                >
                                  <Icon icon={<TrashIcon className="size-6" />} />
                                </Button>
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </div>
          ) : (
            // Single choice: Radio.Group wrapper
            <Radio.Group value={questionData.correctAnswerId} onChange={handleCorrectAnswerChange} className="w-full">
              <DragDropContext onDragEnd={onDragEnd}>
                <Droppable
                  droppableId="answers"
                  renderClone={(provided, snapshot, rubric) => {
                    const answer = questionData.answers[rubric.source.index];
                    return (
                      <div
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        ref={provided.innerRef}
                        className="flex items-start gap-3 rounded-lg bg-neutral-50 p-3"
                      >
                        <div className="cursor-move">
                          <Icon icon={<Bars3Icon className="size-4 text-neutral-400" />} />
                        </div>
                        <Radio value={answer.option_index.toString()} />
                        <div className="flex-1">
                          <RichTextEditor
                            content={answer.option_name}
                            placeholder="Nhập đáp án"
                            className="pointer-events-none w-full"
                            maxLength={200}
                          />
                        </div>
                      </div>
                    );
                  }}
                >
                  {(provided) => (
                    <div {...provided.droppableProps} ref={provided.innerRef} className="relative space-y-3">
                      {questionData.answers.map((answer, index) => (
                        <Draggable key={answer.option_index} draggableId={answer.option_index.toString()} index={index}>
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className={`flex items-start gap-3 rounded-lg py-3 ${
                                snapshot.isDragging ? 'opacity-50' : 'bg-white'
                              }`}
                              style={provided.draggableProps.style}
                            >
                              <div {...provided.dragHandleProps} className="mt-2 cursor-move">
                                <HolderOutlined className="text-xl" />
                              </div>

                              <Radio value={answer.option_index.toString()} className="mt-2" />

                              <div className="flex-1">
                                <RichTextEditor
                                  content={answer.option_name}
                                  placeholder="Nhập đáp án"
                                  onChange={(content) => handleAnswerTextChange(answer.option_index, content)}
                                  onBlur={handleAnswerTextBlur}
                                  className="w-full"
                                  maxLength={200}
                                />
                                {answer.option_thumbnail_image && (
                                  <div className="relative mt-2 inline-block">
                                    <img
                                      src={answer.option_thumbnail_image}
                                      alt="Answer"
                                      className="w-60 rounded object-cover"
                                    />
                                    <Button
                                      variant="ghost"
                                      size="small"
                                      onClick={() => handleRemoveAnswerImage(answer.option_index)}
                                      className="absolute -right-2 -top-2 inline h-auto text-black"
                                    >
                                      <Icon icon={<TrashIcon className="size-6" />} />
                                    </Button>
                                  </div>
                                )}
                              </div>

                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="small"
                                  onClick={() => openMediaLibrary(answer.option_index)}
                                  className="flex items-center text-black"
                                >
                                  <Icon icon={<PhotoIcon className="size-6" />} />
                                </Button>

                                <Button
                                  variant="ghost"
                                  size="small"
                                  onClick={() => showDeleteConfirmation(answer.option_index)}
                                  className="flex items-center text-black"
                                >
                                  <Icon icon={<TrashIcon className="size-6" />} />
                                </Button>
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </Radio.Group>
          )}
        </div>

        {/* Fixed Add Answer Button */}
        <div className="shrink-0 border-t border-neutral-100 p-2">
          <Button
            variant="ghost"
            onClick={addQuestionOption}
            className="flex items-center gap-2 font-poppins text-[#2E2EE5]"
            startIcon={<Icon icon={<PlusIcon className="size-4" />} />}
          >
            Thêm đáp án
          </Button>
        </div>
      </div>

      {showLibraryModal && (
        <LibraryModal
          open={showLibraryModal}
          title={selectedAnswerForMedia === 'question' ? 'Chọn hình ảnh/video' : 'Chọn hình ảnh'}
          enabledTypes={
            selectedAnswerForMedia === 'question'
              ? [LibraryFileType.IMAGE, LibraryFileType.VIDEO]
              : [LibraryFileType.IMAGE]
          }
          limitSize={20 * 1024}
          onAddFile={handleAddMedia}
          onClose={() => {
            setShowLibraryModal(false);
            setSelectedAnswerForMedia('');
          }}
        />
      )}

      <Modal
        open={showDeleteModal}
        onClose={cancelDelete}
        title={'Xoá Câu hỏi nhanh này ra khỏi bài giảng?'}
        footer={
          <div className="flex items-center justify-end gap-3 px-6 py-4">
            <Button variant="tertiary" onClick={cancelDelete} className="font-poppins">
              Hủy thao tác
            </Button>
            <Button variant="error" onClick={handleRemoveAnswer} className="font-poppins">
              Xoá Câu hỏi nhanh
            </Button>
          </div>
        }
      >
        <div className="flex flex-col gap-4 px-6 py-4">
          <Typography variant="bodyLg" className="font-poppins">
            Bạn có chắc chắn muốn xoá bài tập tương tác. Câu hỏi nhanh này sẽ không thể khôi phục. Các tuy chỉnh của Câu
            hỏi nhanh sẽ không thể khôi phục.
          </Typography>
          <Typography variant="bodyLg" className="font-poppins">
            Bạn có muốn tiếp tục?
          </Typography>
        </div>
      </Modal>
    </div>
  );
}
