export const calculateDurationAfterZoom = ({
  duration,
  zoomLevel,
  cursorPosition,
  currentTime,
}: {
  duration: number;
  zoomLevel: number;
  cursorPosition: number;
  currentTime?: number;
}) => {
  // At 0% zoom: show full duration of the video
  if (zoomLevel <= 0) {
    return {
      rulerEnd: duration,
      rulerStart: 0,
      displayedDuration: duration,
    };
  }

  // Calculate zoom factor (0 to 1, where 1 is maximum zoom)
  const zoomFactor = Math.min(zoomLevel / 100, 1);

  // Calculate displayed duration: from full duration down to 600 seconds based on zoom
  const maxDisplayDuration = duration;
  const minDisplayDuration = 600; // 10 minutes at max zoom
  const displayedDuration = maxDisplayDuration - zoomFactor * (maxDisplayDuration - minDisplayDuration);

  // Get current indicator position in absolute time
  const indicatorTime = currentTime ?? calculateTime({ position: cursorPosition, duration });

  // Center the viewport around the indicator position
  const halfDuration = displayedDuration / 2;
  const centeredStart = indicatorTime - halfDuration;
  const centeredEnd = indicatorTime + halfDuration;

  const isViewportStartBeforeVideoStart = centeredStart < 0;
  if (isViewportStartBeforeVideoStart) {
    return {
      rulerStart: 0,
      rulerEnd: Math.min(displayedDuration, duration),
      displayedDuration: Math.min(displayedDuration, duration),
    };
  }

  const isViewportEndAfterVideoEnd = centeredEnd > duration;
  if (isViewportEndAfterVideoEnd) {
    return {
      rulerStart: Math.max(0, duration - displayedDuration),
      rulerEnd: duration,
      displayedDuration: Math.min(displayedDuration, duration),
    };
  }

  // Return centered viewport when within bounds
  return {
    rulerStart: centeredStart,
    rulerEnd: centeredEnd,
    displayedDuration,
  };
};

export const calculateRelativePosition = ({
  absoluteTime,
  rulerStart,
  displayedDuration,
}: {
  absoluteTime: number;
  rulerStart: number;
  displayedDuration: number;
}) => {
  const relativeTime = absoluteTime - rulerStart;
  return Math.max(0, Math.min(100, (relativeTime / displayedDuration) * 100));
};

export const calculateAbsoluteTime = ({
  relativePosition,
  rulerStart,
  displayedDuration,
}: {
  relativePosition: number;
  rulerStart: number;
  displayedDuration: number;
}) => {
  const relativeTime = (relativePosition / 100) * displayedDuration;
  return rulerStart + relativeTime;
};

export const getTimeAfterRulerChange = ({
  event,
  calculatedDuration,
}: {
  event: React.MouseEvent<HTMLDivElement>;
  calculatedDuration: number;
}) => {
  const rect = event.currentTarget.getBoundingClientRect();
  const clickX = event.clientX - rect.left;
  const rulerWidth = rect.width;

  // Calculate percentage of click position (0 to 1)
  const percentage = Math.max(0, Math.min(1, clickX / rulerWidth));
  // Calculate time based on percentage of displayed duration (not absolute duration)
  const timeInSeconds = percentage * calculatedDuration;

  return timeInSeconds;
};

/**
 * Calculates if viewport should scroll when video is playing
 * and updates ruler boundaries accordingly
 */
export const calculateViewportScroll = ({
  currentTime,
  rulerStart,
  rulerEnd,
  displayedDuration,
  totalDuration,
}: {
  currentTime: number;
  rulerStart: number;
  rulerEnd: number;
  displayedDuration: number;
  totalDuration: number;
}) => {
  // Check if indicator is about to pass the end of current viewport
  const viewportEndThreshold = rulerEnd - displayedDuration;

  const shouldScroll = currentTime >= viewportEndThreshold && rulerEnd < totalDuration;

  if (shouldScroll) {
    // Scroll viewport to show next segment
    const newRulerStart = rulerStart + displayedDuration;
    const newRulerEnd = Math.min(totalDuration, newRulerStart + displayedDuration);

    return {
      rulerStart: newRulerStart,
      rulerEnd: newRulerEnd,
      shouldScroll: true,
    };
  }

  return {
    rulerStart,
    rulerEnd,
    shouldScroll: false,
  };
};

export const calculatePositionInPercent = ({ time, duration }: { time: number; duration: number }) => {
  const position = (time / duration) * 100;
  return position;
};

/**
 * Calculate position percentage relative to the current viewport (ruler boundaries)
 * This is used when clicking on the ruler to ensure cursor appears at the correct position
 */
export const calculateRelativePositionInViewport = ({
  absoluteTime,
  rulerStart,
  displayedDuration,
}: {
  absoluteTime: number;
  rulerStart: number;
  displayedDuration: number;
}) => {
  const relativeTime = absoluteTime - rulerStart;
  const position = (relativeTime / displayedDuration) * 100;
  return Math.max(0, Math.min(100, position));
};

export const handleRulerExtension = ({
  displayedDuration,
  totalDuration,
  rulerEnd,
  rulerStart,
  setRulerEnd,
  setRulerStart,
}: {
  displayedDuration: number;
  totalDuration: number;
  rulerEnd: number;
  rulerStart: number;
  setRulerEnd: (value: number) => void;
  setRulerStart: (value: number) => void;
}) => {
  const RULER_EXTENSION_RATIO = 0.2; // Extend ruler by 20% of current duration
  const extension = displayedDuration * RULER_EXTENSION_RATIO;

  const animationDuration = 300;
  const startTime = performance.now();
  const startRulerEnd = rulerEnd;
  const targetRulerEnd = Math.min(totalDuration, rulerEnd + extension);

  const animate = (currentTime: number) => {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / animationDuration, 1);

    // Ease out cubic function for smooth animation
    const easeProgress = 1 - Math.pow(1 - progress, 3);

    const newRulerEnd = startRulerEnd + (targetRulerEnd - startRulerEnd) * easeProgress;
    setRulerEnd(newRulerEnd);

    if (newRulerEnd > totalDuration) {
      const overflow = newRulerEnd - totalDuration;
      setRulerStart(Math.max(0, rulerStart - overflow));
    }

    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  };

  requestAnimationFrame(animate);
};

export const handleRulerBackwardsExtension = ({
  displayedDuration,
  rulerStart,
  setRulerStart,
}: {
  displayedDuration: number;
  rulerStart: number;
  setRulerStart: (value: number) => void;
}) => {
  const RULER_EXTENSION_RATIO_START = 0.2; // Extend ruler by 20% of current duration

  const extension = displayedDuration * RULER_EXTENSION_RATIO_START;
  const newStart = Math.max(0, rulerStart - extension);

  // Animate the ruler start change
  const startTime = performance.now();
  const duration = 300; // Animation duration in milliseconds
  const startValue = rulerStart;
  const change = newStart - startValue;

  const animate = (currentTime: number) => {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // Easing function for smooth animation
    const eased = progress * (2 - progress);
    setRulerStart(startValue + change * eased);

    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  };

  requestAnimationFrame(animate);
};

export const calculateTime = ({ position, duration }: { position: number; duration: number }) => {
  const time = (position / 100) * duration;
  return time;
};
