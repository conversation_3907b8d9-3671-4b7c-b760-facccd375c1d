import { QUERY_KEYS } from '@/constants/query-keys';
import { useSafeSearchParams } from '@/hooks';
import { getTestByIdService, TestPayloadRequest, updateQuizService } from '@/modules/courses/services/test.service';
import { useParams } from 'next/navigation';
import { useMutation, useQuery } from 'react-query';

const useTestActions = () => {
  const params = useParams<{ courseId: string }>();
  const { parsedQueryParams } = useSafeSearchParams<{ sectionId: string; testId: string; lessonId: string }>();
  const courseId = params.courseId;
  const { sectionId, testId, lessonId } = parsedQueryParams;

  const { data: testDetailData } = useQuery({
    queryKey: [QUERY_KEYS.TEST_DETAIL, params.courseId, sectionId, testId],
    queryFn: () => {
      return getTestByIdService({ courseId, sectionId, testId });
    },
    suspense: true,
  });

  const updateTestMutation = useMutation({ mutationFn: updateQuizService });

  const handleUpdateQuiz = (
    payload: TestPayloadRequest,
    mutateOptions?: { onSuccess?: () => void; onError?: () => void },
  ) => {
    const variables = { courseId: params.courseId, sectionId, lectureId: lessonId, testId, payload };
    updateTestMutation.mutate(variables, mutateOptions);
  };

  return {
    testDetail: testDetailData,
    onUpdateQuiz: handleUpdateQuiz,
  };
};

export default useTestActions;
