'use client';

import { Button, Typography } from '@/components/ui';
import { LectureInteract } from '@/modules/courses/types/interaction.type';
import { QuestionOption } from '@/type';import { Checkbox, Radio } from 'antd';
import Image from 'next/image';
import { useEffect, useState } from 'react';



type QuestionContainerProps = {
  onShowCloseButton: () => void;
  onClose: () => void;
  interaction: LectureInteract;
};

function QuestionContainer({ interaction, onShowCloseButton, onClose }: QuestionContainerProps) {
  const [selectedAnswers, setSelectedAnswers] = useState<number[]>([]);
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [showResult, setShowResult] = useState(false);
  const [countdown, setCountdown] = useState<number | null>(null);

  const question = interaction.question;

  const isMultipleChoice = (question?.questionTypeId ?? 0) === 1;
  const correctAnswers = question?.correctAnswer || [];
  const questionOptions = question?.questionOptions || [];

  // Initialize countdown timer if questionDuration exists and questionRequired is false
  useEffect(() => {
    if (question?.questionDuration && question.questionRequired === false) {
      setCountdown(question.questionDuration);
    }
  }, [question?.questionDuration, question?.questionRequired]);

  // Countdown timer effect
  useEffect(() => {
    if (countdown === null || countdown <= 0) {
      if (countdown === 0) {
        onClose();
      }
      return;
    }

    const timer = setInterval(() => {
      setCountdown((prev) => (prev !== null ? prev - 1 : null));
    }, 1000);

    return () => clearInterval(timer);
  }, [countdown, onClose]);
  const handleSingleChoiceChange = (answerId: string) => {
    const answerIndex = parseInt(answerId);
    setSelectedAnswers([answerIndex]);
  };

  const handleMultipleChoiceChange = (optionIndex: number, checked: boolean) => {
    if (checked) {
      setSelectedAnswers([...selectedAnswers, optionIndex]);
    } else {
      setSelectedAnswers(selectedAnswers.filter((id) => id !== optionIndex));
    }
  };

  const handleSubmit = () => {
    if (selectedAnswers.length === 0) return;
    setHasSubmitted(true);
    setShowResult(true);
    onShowCloseButton();
  };

  const isCorrectAnswer = (optionIndex: number) => {
    return correctAnswers.includes(optionIndex);
  };

  const isSelectedAnswer = (optionIndex: number) => {
    return selectedAnswers.includes(optionIndex);
  };

  const isAnswerCorrect = () => {
    if (isMultipleChoice) {
      // For multiple choice: all selected answers must match correct answers exactly
      const sortedSelected = [...selectedAnswers].sort();
      const sortedCorrect = [...correctAnswers].sort();
      return (
        sortedSelected.length === sortedCorrect.length && sortedSelected.every((val, idx) => val === sortedCorrect[idx])
      );
    } else {
      // For single choice: selected answer must match the correct answer
      return selectedAnswers.length === 1 && correctAnswers.includes(selectedAnswers[0]);
    }
  };

  const getOptionBorderStyle = (optionIndex: number) => {
    if (!hasSubmitted) {
      return isSelectedAnswer(optionIndex) ? 'border-blue-500 bg-blue-50' : 'border-neutral-200';
    }

    if (isCorrectAnswer(optionIndex)) {
      return 'border-green-500 bg-green-50';
    } else if (isSelectedAnswer(optionIndex) && !isCorrectAnswer(optionIndex)) {
      return 'border-red-500 bg-red-50';
    }

    return 'border-neutral-200';
  };

  return (
    <div className="mx-auto size-full max-w-screen-sm">
      <div className="flex size-full flex-col bg-white">
        <div className="flex-1 overflow-y-auto p-6">
          <div className="mb-8 flex justify-center">
            <div className="w-full max-w-2xl">
              <div className="grid grid-cols-10 gap-4">
                <div className={question?.questionImage || question?.videoUrl ? 'col-span-6' : 'col-span-10'}>
                  <Typography className="text-center font-poppins text-xl font-bold">
                    {question?.questionName}
                  </Typography>
                </div>
                {question?.questionImage && (
                  <div className="col-span-4 flex justify-end">
                    <Image src={question.questionImage} alt="Question" className="w-full rounded-lg object-cover" />
                  </div>
                )}
                {question?.videoUrl && (
                  <div className="col-span-4 flex justify-end">
                    <video src={question.videoUrl} className="w-full rounded-lg object-cover" controls />
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-center py-5">
            <div className="w-full">
              <div className="">
                {isMultipleChoice ? (
                  <div className="grid w-full grid-cols-2 gap-4">
                    {questionOptions.map((option: QuestionOption, index: number) => (
                      <div
                        key={option.option_index}
                        className={`cursor-pointer rounded-lg border-2 p-4 transition-all ${getOptionBorderStyle(index)} ${hasSubmitted ? 'cursor-default' : 'hover:border-blue-300'}`}
                        onClick={() => !hasSubmitted && handleMultipleChoiceChange(index, !isSelectedAnswer(index))}
                      >
                        <div className="flex items-start gap-3">
                          <Checkbox
                            checked={isSelectedAnswer(index)}
                            onChange={(e) => !hasSubmitted && handleMultipleChoiceChange(index, e.target.checked)}
                            disabled={hasSubmitted}
                          />
                          <div className="grid flex-1 grid-cols-10 gap-2">
                            <div className={option.option_thumbnail_image ? 'col-span-7' : 'col-span-10'}>
                              <div
                                className="prose prose-sm max-w-none"
                                dangerouslySetInnerHTML={{ __html: option.option_name || 'Tùy chọn trống' }}
                              />
                            </div>
                            {option.option_thumbnail_image && (
                              <div className="col-span-3 flex justify-end">
                                <img src={option.option_thumbnail_image} alt="Option" className="h-[67px] rounded object-cover" />
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <Radio.Group
                    value={selectedAnswers[0]?.toString()}
                    onChange={(e) => handleSingleChoiceChange(e.target.value)}
                    disabled={hasSubmitted}
                    className="grid w-full grid-cols-2 gap-4"
                  >
                    {questionOptions.map((option: QuestionOption, index: number) => (
                      <div
                        key={option.option_index}
                        className={`cursor-pointer rounded-lg border-2 p-4 transition-all ${getOptionBorderStyle(index)} ${hasSubmitted ? 'cursor-default' : 'hover:border-blue-300'}`}
                        onClick={() => !hasSubmitted && handleSingleChoiceChange(index.toString())}
                      >
                        <div className="flex items-start gap-3">
                          <Radio value={index.toString()} />
                          <div className="grid flex-1 grid-cols-10 gap-2">
                            <div className={option.option_thumbnail_image ? 'col-span-7' : 'col-span-10'}>
                              <div
                                className="prose prose-sm max-w-none font-nunito"
                                dangerouslySetInnerHTML={{ __html: option.option_name || 'Tùy chọn trống' }}
                              />
                            </div>
                            {option.option_thumbnail_image && (
                              <div className="col-span-3 flex justify-end">
                                <img src={option.option_thumbnail_image} alt="Option" className="h-[67px] rounded object-cover" />
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </Radio.Group>
                )}
              </div>
            </div>
          </div>

          {showResult && (
            <div className="mt-6 flex justify-center">
              <div className="w-full">
                {isAnswerCorrect() ? (
                  <div className="rounded-lg p-4">
                    <div className="mb-2 flex items-center justify-center gap-2">
                      <Image src="/images/quick-question/correct.png" alt="Check" width={40} height={40} />
                      <Typography className="font-poppins text-xl font-semibold text-green-600">Chính xác!</Typography>
                    </div>
                    {interaction.question?.replyRightAnswer && (
                      <Typography className="text-black">{interaction.question?.replyRightAnswer}</Typography>
                    )}
                  </div>
                ) : (
                  <div className="rounded-lg p-4">
                    <div className="mb-2 flex items-center justify-center gap-2">
                      <Image src="/images/quick-question/wrong.png" alt="Wrong" width={40} height={40} />
                      <Typography className="font-poppins text-xl font-semibold text-red-600">
                        Không chính xác!
                      </Typography>
                    </div>
                    {interaction.question?.replyWrongAnswer && (
                      <Typography className="text-black">{interaction.question?.replyWrongAnswer}</Typography>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="p-4">
          <div className="flex flex-col items-center gap-3">
            <div className="">
              {!hasSubmitted ? (
                <Button
                  onClick={handleSubmit}
                  disabled={selectedAnswers.length === 0}
                  className={`${selectedAnswers.length === 0 ? 'cursor-not-allowed bg-neutral-300' : 'bg-primary hover:bg-blue-700'} w-[207px] text-white`}
                >
                  Nộp bài
                </Button>
              ) : (
                question?.questionRequired && (
                  <Button onClick={onClose} className={`w-[207px] bg-primary text-white hover:bg-blue-700`}>
                    Tiếp tục bài học
                  </Button>
                )
              )}
            </div>

            {/* Countdown display */}
            {countdown !== null && question?.questionDuration && question.questionRequired === false && (
              <div className="text-center">
                <Typography variant="bodyMd" className="text-neutral-600">
                  Tự động đóng sau: <span className="font-semibold text-red-500">{countdown}s</span>
                </Typography>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default QuestionContainer;
