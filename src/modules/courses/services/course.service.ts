import { API_ENDPOINTS, HTTP_METHOD } from '@/constants/api';
import { PublishCourseRequest } from '@/features/courses';
import { fetcher } from '@/lib/fetcher';
import {
  LessonCreateRequest,
  LessonDeleteRequest,
  LessonEditRequest,
  SectionCreateRequest,
  SectionEditRequest,
} from '../types/course-request.type';
import { CourseInfo, Lecture } from '../types/course.type';

export const getCourseByIdService = async (courseId: string): Promise<CourseInfo | null> => {
  const response = await fetcher<CourseInfo>(API_ENDPOINTS.COURSES.GET.COURSE_DETAIL.replace(':courseId', courseId));
  return response.data || null;
};

export const publishCourseService = async (payload: PublishCourseRequest): Promise<void> => {
  const url = API_ENDPOINTS.COURSES.PATCH.PUBLISH_COURSE.replace(':courseId', payload.courseId);
  await fetcher(url, { method: HTTP_METHOD.PATCH });
};

export const createSectionService = async (payload: SectionCreateRequest) => {
  const data = {
    section_name: payload.sectionName,
    sort_index: payload.sortIndex,
    section_type: payload.sectionType,
  };

  const url = API_ENDPOINTS.COURSES.POST.SECTION.replace(':courseId', payload.courseId);
  const response = await fetcher<{ id: string }>(url, {
    method: HTTP_METHOD.POST,
    body: JSON.stringify(data),
  });

  return response.data;
};

export const deleteSectionService = async (payload: { courseId: string; sectionId: string }) => {
  const url = API_ENDPOINTS.COURSES.DELETE.SECTION.replace(':courseId', payload.courseId).replace(
    ':sectionId',
    payload.sectionId,
  );

  const res = await fetcher<{ id: string }>(url, {
    method: HTTP_METHOD.DELETE,
  });

  return res.data;
};

export const editSectionService = async (payload: SectionEditRequest) => {
  const data = { section_name: payload.sectionName };

  const url = API_ENDPOINTS.COURSES.PUT.SECTION.replace(':courseId', payload.courseId).replace(
    ':sectionId',
    payload.sectionId,
  );
  const response = await fetcher<{ id: string }>(url, {
    method: HTTP_METHOD.PUT,
    body: JSON.stringify(data),
  });

  return response.data;
};

export const createLessonService = async (payload: LessonCreateRequest) => {
  const url = API_ENDPOINTS.COURSES.POST.LECTURE.replace(':courseId', payload.courseId).replace(
    ':sectionId',
    payload.sectionId,
  );

  const data = {
    lecture_name: payload.lectureName,
    lecture_type: payload.lectureType,
    sort_index: payload.sortIndex,
    thumbnail_file_id: payload?.thumbnailFileId,
  };

  const response = await fetcher<{ id: string }>(url, {
    method: HTTP_METHOD.POST,
    body: JSON.stringify(data),
  });

  return response.data;
};

export const editLessonService = async (payload: LessonEditRequest) => {
  const url = API_ENDPOINTS.COURSES.PUT.LECTURE.replace(':courseId', payload.courseId)
    .replace(':sectionId', payload.sectionId)
    .replace(':lectureId', payload.lectureId);

  const data = {
    lecture_name: payload?.lectureName,
    lecture_thumbnail_image_id: payload?.thumbnailFileId,
  };

  const response = await fetcher<{ id: string }>(url, {
    method: HTTP_METHOD.PUT,
    body: JSON.stringify(data),
  });

  return response.data;
};

export const deleteLessonService = async (payload: LessonDeleteRequest) => {
  const url = API_ENDPOINTS.COURSES.DELETE.LECTURE.replace(':courseId', payload.courseId)
    .replace(':sectionId', payload.sectionId)
    .replace(':lectureId', payload.lectureId);

  const response = await fetcher<{ id: string }>(url, { method: HTTP_METHOD.DELETE });

  return response.data;
};

export const swapSectionService = async (payload: {
  courseId: string;
  sectionId: string;
  sourceIndex: number;
  destinationIndex: number;
}) => {
  const url = API_ENDPOINTS.COURSES.PATCH.SWAP_SECTION.replace(':courseId', payload.courseId).replace(
    ':sectionId',
    payload.sectionId,
  );
  const data = { from: payload.sourceIndex, to: payload.destinationIndex };
  const response = await fetcher<{ id: string }>(url, { method: HTTP_METHOD.PATCH, body: JSON.stringify(data) });
  return response.data;
};

export const swapLessonService = async (payload: {
  courseId: string;
  sectionId: string;
  lectureId: string;
  sourceIndex: number;
  destinationIndex: number;
}) => {
  const url = API_ENDPOINTS.COURSES.PATCH.SWAP_LECTURE.replace(':courseId', payload.courseId)
    .replace(':sectionId', payload.sectionId)
    .replace(':lectureId', payload.lectureId);

  const data = { from: payload.sourceIndex, to: payload.destinationIndex };

  const response = await fetcher<{ id: string }>(url, { method: HTTP_METHOD.PATCH, body: JSON.stringify(data) });
  return response.data;
};

export const getLessonById = async (payload: { courseId: string; sectionId: string; lectureId: string }) => {
  const url = API_ENDPOINTS.COURSES.GET.LECTURE_DETAIL.replace(':courseId', payload.courseId)
    .replace(':sectionId', payload.sectionId)
    .replace(':lectureId', payload.lectureId);

  const response = await fetcher<Lecture>(url, { method: HTTP_METHOD.GET });
  return response.data;
};

export const updateFileLessonService = async (payload: {
  courseId: string;
  sectionId: string;
  lectureId: string;
  payload: { fileId: string | undefined };
}) => {
  const url = API_ENDPOINTS.COURSES.PATCH.LECTURE_VIDEO.replace(':courseId', payload.courseId)
    .replace(':sectionId', payload.sectionId)
    .replace(':lectureId', payload.lectureId);

  const body = JSON.stringify({ file_id: payload.payload.fileId });

  const response = await fetcher<{ id: string }>(url, { method: HTTP_METHOD.PATCH, body });
  return response.data;
};

export const createFaqService = async (payload: {
  courseId: string;
  faqs: {
    question: string;
    answer: string;
    sort_index: number;
  }[];
}) => {
  const url = API_ENDPOINTS.COURSES.POST.CREATE_FAQ.replace(':courseId', payload.courseId);
  const body = JSON.stringify({ faqs: payload.faqs });
  const response = await fetcher<{ id: string }>(url, {
    method: HTTP_METHOD.POST,
    body,
  });
  return response.data;
};

export const editFaqService = async (payload: {
  courseId: string;
  faqId: string;
  question: string;
  answer: string;
}) => {
  const url = API_ENDPOINTS.COURSES.PUT.EDIT_FAQ.replace(':courseId', payload.courseId).replace(
    ':faqId',
    payload.faqId,
  );
  const body = JSON.stringify({ question: payload.question, answer: payload.answer });
  const response = await fetcher<{ id: string }>(url, {
    method: HTTP_METHOD.PUT,
    body,
  });
  return response.data;
};

export const deleteFaqService = async (payload: { courseId: string; faqId: string }) => {
  const url = API_ENDPOINTS.COURSES.DELETE.DELETE_FAQ.replace(':courseId', payload.courseId).replace(
    ':faqId',
    payload.faqId,
  );
  const response = await fetcher<{ id: string }>(url, { method: HTTP_METHOD.DELETE });
  return response.data;
};

export const swapFaqService = async (payload: { courseId: string; from: number; to: number }) => {
  const url = API_ENDPOINTS.COURSES.PATCH.SWAP_FAQ.replace(':courseId', payload.courseId);
  const body = JSON.stringify({ from: payload.from, to: payload.to });
  const response = await fetcher<{ id: string }>(url, { method: HTTP_METHOD.PATCH, body });
  return response.data;
};
