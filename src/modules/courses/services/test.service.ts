import { API_ENDPOINTS, HTTP_METHOD } from '@/constants/api';
import { fetcher } from '@/lib/fetcher';
import { mapQuizResponse } from '@/modules/courses/features/create-course/design-step/utils/test.util';
import { Test } from '@/modules/courses/types/test.type';
import { formatApiUrl } from '@/utils/url.util';

export type QuestionPayloadRequest = {
  question_name: string;
  question_image_file?: string;
  video_file?: string;
  question_options: Array<{
    option_index: number;
    option_name: string;
    option_thumbnail_image_file?: string;
  }>;
  correct_answer: number[];
  sort_index: number;
};

export type TestPayloadRequest = {
  test_type: 'QUIZ' | 'FINAL_TEST';
  content: {
    test_name: string;
    has_limit_time: number;
    limit_time: number;
    questions: QuestionPayloadRequest[];
  };
};

export type UpdateTestRequest = {
  courseId: string;
  sectionId: string;
  lectureId: string;
  testId: string;
  payload: TestPayloadRequest;
};

export type FileResponse = {
  id: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
};

export type OptionResponse = {
  option_index: number;
  option_name: string;
  option_thumbnail_image_file?: FileResponse;
};

export type QuestionResponse = {
  id: string;
  question_name: string;
  question_image: string;
  question_image_file?: FileResponse;
  question_type_id: number;
  video_file?: FileResponse;
  question_options: OptionResponse[];
  correct_answer: number[];
  sort_index: number;
};

export type GetTestResponse = {
  id: string;
  test_name: string;
  min_correct_answer: number;
  has_limit_time: number;
  limit_time: number;
  questions: QuestionResponse[];
  created_at: string;
  updated_at: string;
};

export const getTestByIdService = async (payload: {
  courseId: string;
  sectionId: string;
  testId: string;
}): Promise<Test | null> => {
  const url = formatApiUrl(API_ENDPOINTS.COURSES.GET.TEST_DETAIL, {
    courseId: payload.courseId,
    sectionId: payload.sectionId,
    testId: payload.testId,
  });
  const response = await fetcher<GetTestResponse>(url);

  const mappedResponse = response.data ? mapQuizResponse(response.data) : null;
  return mappedResponse;
};

export const updateQuizService = async (payload: UpdateTestRequest) => {
  const url = formatApiUrl(API_ENDPOINTS.COURSES.PUT.QUIZ, {
    courseId: payload.courseId,
    sectionId: payload.sectionId,
    lectureId: payload.lectureId,
    testId: payload.testId,
  });

  const res = await fetcher(url, { method: HTTP_METHOD.PUT, body: JSON.stringify(payload.payload) });
  return res.data;
};
