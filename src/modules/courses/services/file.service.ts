import { API_ENDPOINTS, HTTP_METHOD } from '@/constants/api';
import { CourseListBase } from '@/features/courses';
import { fetcher } from '@/lib/fetcher';
import { LibraryFile } from '@/modules/courses/types/file.type';
import { PaginatedQueryBase } from '@/type';
import queryString from 'query-string';
import { LibraryFileType } from '../constants/file.const';

export const getUserFiles = async (
  payload: { fileTypes: LibraryFileType; fileName?: string } & Partial<PaginatedQueryBase>,
) => {
  const { fileTypes, fileName, limit = 10, page = 0 } = payload;
  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.USERS.GET.FILES,
    query: { fileTypes, limit, page, fileName },
  });

  const { data } = await fetcher<CourseListBase<LibraryFile>>(query);

  const list: LibraryFile[] = data?.data || [];
  return { list, total: data?.count || 0 };
};

export const uploadFile = async ({ payload }: { payload: FormData }): Promise<LibraryFile> => {
  const config = { body: payload, method: HTTP_METHOD.POST };

  const res = await fetcher(API_ENDPOINTS.USERS.POST.UPLOAD_FILE, config);
  return res.data as LibraryFile;
};

export const deleteFile = async (id: string) => {
  const query = API_ENDPOINTS.USERS.DELETE.DELETE_FILE.replace(':id', id);

  const res = await fetcher(query, { method: HTTP_METHOD.DELETE });
  return res.data;
};

export const editFileName = async (payload: { fileName: string; id: string }) => {
  const query = API_ENDPOINTS.USERS.PATCH.RENAME_FILE.replace(':id', payload.id);

  const body = JSON.stringify({ file_name: payload.fileName });

  const res = await fetcher(query, { body, method: HTTP_METHOD.PATCH });

  return res.data;
};
