import { HTTP_STATUS_CODE } from '@/constants/api';
import { App } from 'antd';
import { useMutation } from 'react-query';
import { deleteFile, editFileName, uploadFile } from '../../services/file.service';

export const useFileActions = () => {
  const { notification } = App.useApp();

  const uploadFileMutation = useMutation({
    mutationFn: (variables: { payload: FormData }) => uploadFile(variables),
    onSuccess: async () => {
      notification.success({ message: 'Upload file thành công' });
    },
    onError: () => {
      notification.error({ message: 'Upload file thất bại' });
    },
  });

  const deleteFileMutation = useMutation({
    mutationFn: (variables: { id: string }) => deleteFile(variables.id),
    onSuccess: async () => {
      notification.success({ message: 'Xóa file thành công' });
    },
    onError: (error) => {
      const errorResponse = error as { message: string; status: number };
      const errorMessage = errorResponse?.message;

      if (errorResponse.status === HTTP_STATUS_CODE.CONFLICT) {
        notification.error({
          message:
            'Tệp này đang được sử dụng trong các khóa học của bạn. Vui lòng gỡ bỏ tệp khỏi các bài học trước khi xóa.',
        });
        return;
      }

      notification.error({ message: errorMessage || 'Xóa file thất bại' });
    },
  });

  const updateFileNameMutation = useMutation({
    mutationFn: (variables: { id: string; fileName: string }) => editFileName(variables),
    onSuccess: async () => {
      notification.success({ message: 'Cập nhật tên file thành công' });
    },
    onError: () => {
      notification.error({ message: 'Cập nhật tên file thất bại' });
    },
  });

  return { uploadFileMutation, deleteFileMutation, updateFileNameMutation };
};
