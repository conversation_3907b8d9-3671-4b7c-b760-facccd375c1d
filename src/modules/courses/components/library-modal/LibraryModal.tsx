'use client';

import { <PERSON><PERSON>, Modal } from '@/components/ui';
import React from 'react';
import { match } from 'ts-pattern';

import Searchbar from '@/components/client/search-bar/Searchbar';
import { QUERY_KEYS } from '@/constants/query-keys';
import { useDebounce } from '@/hooks';
import { useClient } from '@/hooks/useClient';
import CardContentSkeleton from '@/modules/courses/components/library-modal/CardContentSkeleton';
import DocumentContentSkeleton from '@/modules/courses/components/library-modal/DocumentContentSkeleton';
import { LibraryModalProvider } from '@/modules/courses/components/library-modal/LibraryModalProvider';
import { getUserFiles } from '@/modules/courses/services/file.service';
import { LibraryFile } from '@/modules/courses/types/file.type';
import dynamic from 'next/dynamic';
import { useQuery } from 'react-query';
import { LibraryFileType, libraryFileTypeName } from '../../constants/file.const';
import LibrarySidebar from './LibrarySidebar';
import UploadModal from './UploadModal';

const VideoImageContent = dynamic(() => import('./VideoImageContent'), {
  ssr: false,
  loading: () => <CardContentSkeleton />,
});
const DocumentContent = dynamic(() => import('./DocumentContent').then((mod) => mod.DocumentContent), {
  ssr: false,
  loading: () => <DocumentContentSkeleton />,
});

type LibraryModalProps = {
  open: boolean;
  title: string;

  // enabledTypes: specifies which file types are available in the library modal (e.g., IMAGE, VIDEO, DOCUMENT)
  enabledTypes: LibraryFileType[];

  // limitSize: optional size limit in KB - files larger than this will be disabled
  limitSize?: number;

  selectMode?: {
    [key in LibraryFileType]?: 'single' | 'multiple';
  };

  onAddFile?: (file: LibraryFile) => void;
  onAddFiles?: (files: LibraryFile[]) => void;
  onClose?: () => void;
};

const useUserFilesQuery = ({ selectedType, fileName }: { selectedType: LibraryFileType; fileName: string }) => {
  const debouncedFileName = useDebounce(fileName, 500);

  const { data, isLoading, refetch } = useQuery({
    queryKey: [QUERY_KEYS.USER_FILES, selectedType, debouncedFileName],
    queryFn: () =>
      getUserFiles({
        fileTypes: selectedType as LibraryFileType,
        limit: 1000,
        page: 0,
        fileName: debouncedFileName || undefined,
      }),
    enabled:
      [LibraryFileType.IMAGE, LibraryFileType.VIDEO, LibraryFileType.DOCUMENT].includes(selectedType) &&
      debouncedFileName.length >= 0,
  });

  const files = data?.list || [];

  return { files, isLoading, refetch };
};

const useLibraryModal = (enabledTypes: LibraryFileType[]) => {
  const [selectedType, setSelectedType] = React.useState<LibraryFileType>(enabledTypes?.[0]);
  const [selectedState, setSelectedState] = React.useState<{
    [LibraryFileType.IMAGE]: Set<string>;
    [LibraryFileType.VIDEO]: Set<string>;
    [LibraryFileType.DOCUMENT]: Set<string>;
  }>({
    [LibraryFileType.IMAGE]: new Set(),
    [LibraryFileType.VIDEO]: new Set(),
    [LibraryFileType.DOCUMENT]: new Set(),
  });

  const selected = selectedState[selectedType];
  const noSelection = selected.size === 0;

  const handleSelectFile = (id: string) => {
    setSelectedState((prev) => ({
      ...prev,
      [selectedType]: new Set([id]),
    }));
  };

  const handleSelectFiles = (ids: string[]) => {
    setSelectedState((prev) => ({
      ...prev,
      [selectedType]: new Set(ids),
    }));
  };

  return {
    noSelection,
    selectedType,
    selected,
    selectedState,
    setSelectedState,

    onSelectType: (type: LibraryFileType) => setSelectedType(type),
    onSelectFile: handleSelectFile,
    onSelectFiles: handleSelectFiles,
  };
};

function LibraryModal(props: LibraryModalProps) {
  const {
    open,
    title,
    enabledTypes,
    limitSize,
    selectMode = {
      [LibraryFileType.IMAGE]: 'single',
      [LibraryFileType.VIDEO]: 'single',
      [LibraryFileType.DOCUMENT]: 'multiple',
    },
    onClose = () => {},
    onAddFile = () => {},
    onAddFiles = () => {},
  } = props;

  const { isClient } = useClient();

  const [openUploadModal, setOpenUploadModal] = React.useState(false);

  const [search, setSearch] = React.useState('');

  const {
    selectedType,
    noSelection,
    selected,
    selectedState,
    setSelectedState,
    onSelectFile,
    onSelectFiles,
    onSelectType,
  } = useLibraryModal(enabledTypes);

  const [allFiles, setAllFiles] = React.useState<LibraryFile[]>([]);
  const { files, isLoading, refetch } = useUserFilesQuery({ selectedType, fileName: search });

  const handleAddFile = () => {
    const selectedFile = files.find((file) => selected.has(file.id));

    if (selectedFile) {
      onAddFile?.(selectedFile);
      onClose?.();
    }
  };

  const handleAddFiles = () => {
    const allSelectedFiles: LibraryFile[] = [];

    Object.entries(selectedState).forEach(([type, selectedIds]) => {
      const selectedIdsSet = selectedIds as Set<string>;
      if (selectedIdsSet.size > 0) {
        const typeFiles = allFiles.filter((file) => file.fileType === type && selectedIdsSet.has(file.id));
        allSelectedFiles.push(...typeFiles);
      }
    });

    if (allSelectedFiles.length > 0) {
      onAddFiles?.(allSelectedFiles);
      setSelectedState({
        [LibraryFileType.IMAGE]: new Set(),
        [LibraryFileType.VIDEO]: new Set(),
        [LibraryFileType.DOCUMENT]: new Set(),
      });
    }
  };

  const handleAdd = () => {
    const currentMode = selectMode[selectedType];

    if (currentMode === 'single') {
      handleAddFile();
    } else {
      handleAddFiles();
    }
  };

  const renderContent = () => {
    return match(selectedType)
      .with(LibraryFileType.IMAGE, () => {
        return (
          <VideoImageContent
            selectMode={selectMode[LibraryFileType.IMAGE]}
            selected={selected}
            onSelect={onSelectFile}
            onSelectMultiple={onSelectFiles}
          />
        );
      })
      .with(LibraryFileType.VIDEO, () => {
        return (
          <VideoImageContent
            selectMode={selectMode[LibraryFileType.VIDEO]}
            selected={selected}
            onSelect={onSelectFile}
            onSelectMultiple={onSelectFiles}
          />
        );
      })
      .with(LibraryFileType.DOCUMENT, () => {
        return (
          <DocumentContent
            selectMode={selectMode[LibraryFileType.DOCUMENT]}
            selected={selected}
            onSelect={onSelectFile}
            onSelectMultiple={onSelectFiles}
          />
        );
      })
      .exhaustive();
  };

  React.useEffect(() => {
    if (files) {
      setAllFiles((prev) => [...prev, ...files.filter((f) => !prev.some((p) => p.id === f.id))]);
    }
  }, [files]);

  // Prevent SSR rendering to avoid hydration mismatch
  if (!isClient) {
    return null;
  }

  return (
    <LibraryModalProvider value={{ selectedType, files, isLoading, enabledTypes, limitSize, refetch }}>
      <Modal
        open={open}
        width="70%"
        onClose={onClose}
        title={title}
        classNames={{ body: 'border-t border-neutral-200' }}
        footer={
          <div className="flex justify-end border-t border-t-neutral-200 p-6">
            <Button variant="primary" color="primary" disabled={noSelection} onClick={handleAdd}>
              {`Thêm ${libraryFileTypeName[selectedType]} vào khóa học`}
            </Button>
          </div>
        }
      >
        <div className="flex size-full">
          <LibrarySidebar onUpload={() => setOpenUploadModal(true)} onSelectType={onSelectType} />

          <div className="flex w-full flex-col gap-4 p-4">
            <Searchbar
              inputProps={{
                placeholder: `Tìm kiếm ${libraryFileTypeName[selectedType]} có sẵn trong thư viện`,
                onChange: (e) => setSearch(e.target.value),
              }}
            />

            <div className="relative h-[500px] overflow-y-auto">{renderContent()}</div>
          </div>
        </div>
      </Modal>

      {openUploadModal && (
        <UploadModal open={openUploadModal} onClose={() => setOpenUploadModal(false)} limitSize={limitSize} />
      )}
    </LibraryModalProvider>
  );
}

export default LibraryModal;
