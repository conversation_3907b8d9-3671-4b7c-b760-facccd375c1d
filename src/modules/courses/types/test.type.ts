import { LibraryFile } from '@/modules/courses/types/file.type';

export type QuizFile = Pick<LibraryFile, 'fileUrl' | 'fileName' | 'id' | 'fileSize'>;

export type Question = {
  id: string;
  questionName: string;
  questionImage: string;
  questionImageFile?: QuizFile;
  questionTypeId: number;
  videoFile?: QuizFile;
  questionOptions: {
    optionIndex: number;
    optionName: string;
    optionThumbnailImageFile?: QuizFile;
  }[];
  correctAnswer: number[];
  sortIndex: number;
};

export type Test = {
  id: string;
  testName: string;
  minCorrectAnswer: number;
  hasLimitTime: number;
  limitTime: number;
  questions: Question[];
  createdAt: string;
  updatedAt: string;
};
