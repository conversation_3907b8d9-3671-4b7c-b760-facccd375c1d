import { routePaths } from 'config';
import { NextFetchEvent, NextRequest, NextResponse } from 'next/server';
import { COOKIE_NAMES } from './constants/auth';

const prefixPrivateRoutes = routePaths.profile.path;

export function middleware(request: NextRequest, _event: NextFetchEvent) {
  // Clone the request headers and set a new header `x-url`
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-url', request.url);

  const info = request.cookies.get(COOKIE_NAMES.USER_INFO)?.value;
  const accessToken = request.cookies.get(COOKIE_NAMES.ACCESS_TOKEN)?.value;
  const isLoggedIn = info && accessToken;

  if (request.nextUrl.pathname.includes(prefixPrivateRoutes) && !isLoggedIn) {
    const originalPath = request.nextUrl.pathname + request.nextUrl.search;
    console.log('request: ', request);
    // console.log('originalPath: ', originalPath);
    const encodedRedirect = encodeURIComponent(originalPath);

    const loginUrl = new URL(routePaths.login, request.url);
    // console.log('loginUrl: ', loginUrl);
    loginUrl.searchParams.set('redirect', encodedRedirect);

    return NextResponse.redirect(loginUrl);
  }

  // You can also set request headers in NextResponse.rewrite
  const response = NextResponse.next({
    request: {
      // New request headers
      headers: requestHeaders,
    },
  });

  // Set a new response header `x-hello-from-middleware2`
  response.headers.set('x-hello-from-middleware2', 'hello');
  return response;
}

export const config = {
  matcher: ['/profile/:path*', '/courses/:path*', '/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
