name: Deploy to EC2 Development

on:
  push:
    branches: [develop]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Set up SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          build-args: |
            AWS_REGION="${{ secrets.AWS_REGION }}"
            NEXT_PUBLIC_BASE_API_URL="${{ secrets.NEXT_PUBLIC_BASE_API_URL_V1 }}"
            NEXT_PUBLIC_METADATA_BASE="${{ secrets.NEXT_PUBLIC_METADATA_BASE_V1 }}"
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/studify-web:latest
          context: .
          file: Dockerfile.dev

  deploy:
    needs: [build]
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to EC2
        env:
          PRIVATE_KEY: ${{ secrets.EC2_PEM }}
        run: |
          echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
          ssh -o StrictHostKeyChecking=no -i private_key ${{ secrets.AWS_EC2_USER }}@${{ secrets.AWS_EC2_HOST }} '

              # Now we have got the access of EC2 and we will start the deploy
              echo "${{ secrets.DOCKERHUB_TOKEN }}" | sudo docker login docker.io -u ${{ secrets.DOCKERHUB_USERNAME }} --password-stdin && 
              sudo docker rm studify-web -f && 
              sudo docker image rm ${{ secrets.DOCKERHUB_USERNAME }}/studify-web && 
              sudo docker image pull ${{ secrets.DOCKERHUB_USERNAME }}/studify-web:latest && 
              sudo docker run --restart=always -p 80:3000 --name studify-web -d ${{ secrets.DOCKERHUB_USERNAME }}/studify-web:latest
              '
